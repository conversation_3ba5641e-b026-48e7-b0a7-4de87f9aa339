(()=>{var _={1191:(_,X,ee)=>{let te=ee(9613).feature;function browsersSort(_,X){_=_.split(" ");X=X.split(" ");if(_[0]>X[0]){return 1}else if(_[0]<X[0]){return-1}else{return Math.sign(parseFloat(_[1])-parseFloat(X[1]))}}function f(_,X,ee){_=te(_);if(!ee){[ee,X]=[X,{}]}let re=X.match||/\sx($|\s)/;let se=[];for(let X in _.stats){let ee=_.stats[X];for(let _ in ee){let te=ee[_];if(te.match(re)){se.push(X+" "+_)}}}ee(se.sort(browsersSort))}let re={};function prefix(_,X){for(let ee of _){re[ee]=Object.assign({},X)}}function add(_,X){for(let ee of _){re[ee].browsers=re[ee].browsers.concat(X.browsers).sort(browsersSort)}}_.exports=re;let se=ee(2861);f(se,(_=>prefix(["border-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius"],{mistakes:["-khtml-","-ms-","-o-"],feature:"border-radius",browsers:_})));let ne=ee(2194);f(ne,(_=>prefix(["box-shadow"],{mistakes:["-khtml-"],feature:"css-boxshadow",browsers:_})));let ie=ee(354);f(ie,(_=>prefix(["animation","animation-name","animation-duration","animation-delay","animation-direction","animation-fill-mode","animation-iteration-count","animation-play-state","animation-timing-function","@keyframes"],{mistakes:["-khtml-","-ms-"],feature:"css-animation",browsers:_})));let oe=ee(40);f(oe,(_=>prefix(["transition","transition-property","transition-duration","transition-delay","transition-timing-function"],{mistakes:["-khtml-","-ms-"],browsers:_,feature:"css-transitions"})));let ae=ee(4602);f(ae,(_=>prefix(["transform","transform-origin"],{feature:"transforms2d",browsers:_})));let le=ee(2866);f(le,(_=>{prefix(["perspective","perspective-origin"],{feature:"transforms3d",browsers:_});return prefix(["transform-style"],{mistakes:["-ms-","-o-"],browsers:_,feature:"transforms3d"})}));f(le,{match:/y\sx|y\s#2/},(_=>prefix(["backface-visibility"],{mistakes:["-ms-","-o-"],feature:"transforms3d",browsers:_})));let ue=ee(2571);f(ue,{match:/y\sx/},(_=>prefix(["linear-gradient","repeating-linear-gradient","radial-gradient","repeating-radial-gradient"],{props:["background","background-image","border-image","mask","list-style","list-style-image","content","mask-image"],mistakes:["-ms-"],feature:"css-gradients",browsers:_})));f(ue,{match:/a\sx/},(_=>{_=_.map((_=>{if(/firefox|op/.test(_)){return _}else{return`${_} old`}}));return add(["linear-gradient","repeating-linear-gradient","radial-gradient","repeating-radial-gradient"],{feature:"css-gradients",browsers:_})}));let ce=ee(6597);f(ce,(_=>prefix(["box-sizing"],{feature:"css3-boxsizing",browsers:_})));let pe=ee(3882);f(pe,(_=>prefix(["filter"],{feature:"css-filters",browsers:_})));let fe=ee(1545);f(fe,(_=>prefix(["filter-function"],{props:["background","background-image","border-image","mask","list-style","list-style-image","content","mask-image"],feature:"css-filter-function",browsers:_})));let de=ee(3166);f(de,{match:/y\sx|y\s#2/},(_=>prefix(["backdrop-filter"],{feature:"css-backdrop-filter",browsers:_})));let he=ee(7801);f(he,(_=>prefix(["element"],{props:["background","background-image","border-image","mask","list-style","list-style-image","content","mask-image"],feature:"css-element-function",browsers:_})));let me=ee(7809);f(me,(_=>{prefix(["columns","column-width","column-gap","column-rule","column-rule-color","column-rule-width","column-count","column-rule-style","column-span","column-fill"],{feature:"multicolumn",browsers:_});let X=_.filter((_=>!/firefox/.test(_)));prefix(["break-before","break-after","break-inside"],{feature:"multicolumn",browsers:X})}));let ge=ee(9474);f(ge,(_=>prefix(["user-select"],{mistakes:["-khtml-"],feature:"user-select-none",browsers:_})));let be=ee(4618);f(be,{match:/a\sx/},(_=>{_=_.map((_=>{if(/ie|firefox/.test(_)){return _}else{return`${_} 2009`}}));prefix(["display-flex","inline-flex"],{props:["display"],feature:"flexbox",browsers:_});prefix(["flex","flex-grow","flex-shrink","flex-basis"],{feature:"flexbox",browsers:_});prefix(["flex-direction","flex-wrap","flex-flow","justify-content","order","align-items","align-self","align-content"],{feature:"flexbox",browsers:_})}));f(be,{match:/y\sx/},(_=>{add(["display-flex","inline-flex"],{feature:"flexbox",browsers:_});add(["flex","flex-grow","flex-shrink","flex-basis"],{feature:"flexbox",browsers:_});add(["flex-direction","flex-wrap","flex-flow","justify-content","order","align-items","align-self","align-content"],{feature:"flexbox",browsers:_})}));let ve=ee(3098);f(ve,(_=>prefix(["calc"],{props:["*"],feature:"calc",browsers:_})));let ye=ee(1188);f(ye,(_=>prefix(["background-origin","background-size"],{feature:"background-img-opts",browsers:_})));let we=ee(5591);f(we,(_=>prefix(["background-clip"],{feature:"background-clip-text",browsers:_})));let xe=ee(1328);f(xe,(_=>prefix(["font-feature-settings","font-variant-ligatures","font-language-override"],{feature:"font-feature",browsers:_})));let ke=ee(3944);f(ke,(_=>prefix(["font-kerning"],{feature:"font-kerning",browsers:_})));let Se=ee(7097);f(Se,(_=>prefix(["border-image"],{feature:"border-image",browsers:_})));let _e=ee(4822);f(_e,(_=>prefix(["::selection"],{selector:true,feature:"css-selection",browsers:_})));let Pe=ee(6215);f(Pe,(_=>{prefix(["::placeholder"],{selector:true,feature:"css-placeholder",browsers:_.concat(["ie 10 old","ie 11 old","firefox 18 old"])})}));let Oe=ee(9278);f(Oe,(_=>{prefix([":placeholder-shown"],{selector:true,feature:"css-placeholder-shown",browsers:_})}));let je=ee(5197);f(je,(_=>prefix(["hyphens"],{feature:"css-hyphens",browsers:_})));let Te=ee(7766);f(Te,(_=>prefix([":fullscreen"],{selector:true,feature:"fullscreen",browsers:_})));f(Te,{match:/x(\s#2|$)/},(_=>prefix(["::backdrop"],{selector:true,feature:"fullscreen",browsers:_})));let Ee=ee(2416);f(Ee,(_=>prefix(["::file-selector-button"],{selector:true,feature:"file-selector-button",browsers:_})));let Fe=ee(7721);f(Fe,(_=>prefix([":autofill"],{selector:true,feature:"css-autofill",browsers:_})));let $e=ee(3247);f($e,(_=>prefix(["tab-size"],{feature:"css3-tabsize",browsers:_})));let Me=ee(5691);let Re=["width","min-width","max-width","height","min-height","max-height","inline-size","min-inline-size","max-inline-size","block-size","min-block-size","max-block-size","grid","grid-template","grid-template-rows","grid-template-columns","grid-auto-columns","grid-auto-rows"];f(Me,(_=>prefix(["max-content","min-content"],{props:Re,feature:"intrinsic-width",browsers:_})));f(Me,{match:/x|\s#4/},(_=>prefix(["fill","fill-available"],{props:Re,feature:"intrinsic-width",browsers:_})));f(Me,{match:/x|\s#5/},(_=>prefix(["fit-content"],{props:Re,feature:"intrinsic-width",browsers:_})));let Ae=ee(7437);f(Ae,(_=>prefix(["stretch"],{props:Re,feature:"css-width-stretch",browsers:_})));let qe=ee(8265);f(qe,(_=>prefix(["zoom-in","zoom-out"],{props:["cursor"],feature:"css3-cursors-newer",browsers:_})));let ze=ee(2983);f(ze,(_=>prefix(["grab","grabbing"],{props:["cursor"],feature:"css3-cursors-grab",browsers:_})));let Ge=ee(8235);f(Ge,(_=>prefix(["sticky"],{props:["position"],feature:"css-sticky",browsers:_})));let Ue=ee(1014);f(Ue,(_=>prefix(["touch-action"],{feature:"pointer",browsers:_})));let He=ee(134);f(He,(_=>prefix(["text-decoration-style","text-decoration-color","text-decoration-line","text-decoration"],{feature:"text-decoration",browsers:_})));f(He,{match:/x.*#[235]/},(_=>prefix(["text-decoration-skip","text-decoration-skip-ink"],{feature:"text-decoration",browsers:_})));let Ze=ee(744);f(Ze,(_=>prefix(["text-size-adjust"],{feature:"text-size-adjust",browsers:_})));let Ke=ee(6649);f(Ke,(_=>{prefix(["mask-clip","mask-composite","mask-image","mask-origin","mask-repeat","mask-border-repeat","mask-border-source"],{feature:"css-masks",browsers:_});prefix(["mask","mask-position","mask-size","mask-border","mask-border-outset","mask-border-width","mask-border-slice"],{feature:"css-masks",browsers:_})}));let Xe=ee(9205);f(Xe,(_=>prefix(["clip-path"],{feature:"css-clip-path",browsers:_})));let et=ee(6781);f(et,(_=>prefix(["box-decoration-break"],{feature:"css-boxdecorationbreak",browsers:_})));let tt=ee(1480);f(tt,(_=>prefix(["object-fit","object-position"],{feature:"object-fit",browsers:_})));let rt=ee(5460);f(rt,(_=>prefix(["shape-margin","shape-outside","shape-image-threshold"],{feature:"css-shapes",browsers:_})));let st=ee(7806);f(st,(_=>prefix(["text-overflow"],{feature:"text-overflow",browsers:_})));let nt=ee(3504);f(nt,(_=>prefix(["@viewport"],{feature:"css-deviceadaptation",browsers:_})));let it=ee(8181);f(it,{match:/( x($| )|a #2)/},(_=>prefix(["@resolution"],{feature:"css-media-resolution",browsers:_})));let ot=ee(2807);f(ot,(_=>prefix(["text-align-last"],{feature:"css-text-align-last",browsers:_})));let lt=ee(8995);f(lt,{match:/y x|a x #1/},(_=>prefix(["pixelated"],{props:["image-rendering"],feature:"css-crisp-edges",browsers:_})));f(lt,{match:/a x #2/},(_=>prefix(["image-rendering"],{feature:"css-crisp-edges",browsers:_})));let ut=ee(7395);f(ut,(_=>prefix(["border-inline-start","border-inline-end","margin-inline-start","margin-inline-end","padding-inline-start","padding-inline-end"],{feature:"css-logical-props",browsers:_})));f(ut,{match:/x\s#2/},(_=>prefix(["border-block-start","border-block-end","margin-block-start","margin-block-end","padding-block-start","padding-block-end"],{feature:"css-logical-props",browsers:_})));let ct=ee(4773);f(ct,{match:/#2|x/},(_=>prefix(["appearance"],{feature:"css-appearance",browsers:_})));let pt=ee(1340);f(pt,(_=>prefix(["scroll-snap-type","scroll-snap-coordinate","scroll-snap-destination","scroll-snap-points-x","scroll-snap-points-y"],{feature:"css-snappoints",browsers:_})));let ft=ee(1949);f(ft,(_=>prefix(["flow-into","flow-from","region-fragment"],{feature:"css-regions",browsers:_})));let dt=ee(2237);f(dt,(_=>prefix(["image-set"],{props:["background","background-image","border-image","cursor","mask","mask-image","list-style","list-style-image","content"],feature:"css-image-set",browsers:_})));let ht=ee(2298);f(ht,{match:/a|x/},(_=>prefix(["writing-mode"],{feature:"css-writing-mode",browsers:_})));let mt=ee(8786);f(mt,(_=>prefix(["cross-fade"],{props:["background","background-image","border-image","mask","list-style","list-style-image","content","mask-image"],feature:"css-cross-fade",browsers:_})));let gt=ee(2478);f(gt,(_=>prefix([":read-only",":read-write"],{selector:true,feature:"css-read-only-write",browsers:_})));let bt=ee(5514);f(bt,(_=>prefix(["text-emphasis","text-emphasis-position","text-emphasis-style","text-emphasis-color"],{feature:"text-emphasis",browsers:_})));let vt=ee(6554);f(vt,(_=>{prefix(["display-grid","inline-grid"],{props:["display"],feature:"css-grid",browsers:_});prefix(["grid-template-columns","grid-template-rows","grid-row-start","grid-column-start","grid-row-end","grid-column-end","grid-row","grid-column","grid-area","grid-template","grid-template-areas","place-self"],{feature:"css-grid",browsers:_})}));f(vt,{match:/a x/},(_=>prefix(["grid-column-align","grid-row-align"],{feature:"css-grid",browsers:_})));let yt=ee(9290);f(yt,(_=>prefix(["text-spacing"],{feature:"css-text-spacing",browsers:_})));let wt=ee(9323);f(wt,(_=>prefix([":any-link"],{selector:true,feature:"css-any-link",browsers:_})));let xt=ee(7511);f(xt,(_=>prefix(["isolate"],{props:["unicode-bidi"],feature:"css-unicode-bidi",browsers:_})));f(xt,{match:/y x|a x #2/},(_=>prefix(["plaintext"],{props:["unicode-bidi"],feature:"css-unicode-bidi",browsers:_})));f(xt,{match:/y x/},(_=>prefix(["isolate-override"],{props:["unicode-bidi"],feature:"css-unicode-bidi",browsers:_})));let kt=ee(3898);f(kt,{match:/a #1/},(_=>prefix(["overscroll-behavior"],{feature:"css-overscroll-behavior",browsers:_})));let St=ee(2834);f(St,(_=>prefix(["color-adjust"],{feature:"css-color-adjust",browsers:_})));let _t=ee(4838);f(_t,(_=>prefix(["text-orientation"],{feature:"css-text-orientation",browsers:_})))},3183:(_,X,ee)=>{let te=ee(4877);class AtRule extends te{add(_,X){let ee=X+_.name;let te=_.parent.some((X=>X.name===ee&&X.params===_.params));if(te){return undefined}let re=this.clone(_,{name:ee});return _.parent.insertBefore(_,re)}process(_){let X=this.parentPrefix(_);for(let ee of this.prefixes){if(!X||X===ee){this.add(_,ee)}}}}_.exports=AtRule},6544:(_,X,ee)=>{let te=ee(4907);let{agents:re}=ee(9613);let se=ee(1437);let ne=ee(1901);let ie=ee(7223);let oe=ee(1191);let ae=ee(1405);let le={browsers:re,prefixes:oe};const ue="\n"+"  Replace Autoprefixer `browsers` option to Browserslist config.\n"+"  Use `browserslist` key in `package.json` or `.browserslistrc` file.\n"+"\n"+"  Using `browsers` option can cause errors. Browserslist config can\n"+"  be used for Babel, Autoprefixer, postcss-normalize and other tools.\n"+"\n"+"  If you really need to use option, rename it to `overrideBrowserslist`.\n"+"\n"+"  Learn more at:\n"+"  https://github.com/browserslist/browserslist#readme\n"+"  https://twitter.com/browserslist\n"+"\n";function isPlainObject(_){return Object.prototype.toString.apply(_)==="[object Object]"}let ce=new Map;function timeCapsule(_,X){if(X.browsers.selected.length===0){return}if(X.add.selectors.length>0){return}if(Object.keys(X.add).length>2){return}_.warn("Autoprefixer target browsers do not need any prefixes."+"You do not need Autoprefixer anymore.\n"+"Check your Browserslist config to be sure that your targets "+"are set up correctly.\n"+"\n"+"  Learn more at:\n"+"  https://github.com/postcss/autoprefixer#readme\n"+"  https://github.com/browserslist/browserslist#readme\n"+"\n")}_.exports=plugin;function plugin(..._){let X;if(_.length===1&&isPlainObject(_[0])){X=_[0];_=undefined}else if(_.length===0||_.length===1&&!_[0]){_=undefined}else if(_.length<=2&&(Array.isArray(_[0])||!_[0])){X=_[1];_=_[0]}else if(typeof _[_.length-1]==="object"){X=_.pop()}if(!X){X={}}if(X.browser){throw new Error("Change `browser` option to `overrideBrowserslist` in Autoprefixer")}else if(X.browserslist){throw new Error("Change `browserslist` option to `overrideBrowserslist` in Autoprefixer")}if(X.overrideBrowserslist){_=X.overrideBrowserslist}else if(X.browsers){if(typeof console!=="undefined"&&console.warn){console.warn(se.red(ue.replace(/`[^`]+`/g,(_=>se.yellow(_.slice(1,-1))))))}_=X.browsers}let ee={ignoreUnknownVersions:X.ignoreUnknownVersions,stats:X.stats,env:X.env};function loadPrefixes(te){let re=le;let se=new ne(re.browsers,_,te,ee);let oe=se.selected.join(", ")+JSON.stringify(X);if(!ce.has(oe)){ce.set(oe,new ie(re.prefixes,se,X))}return ce.get(oe)}return{postcssPlugin:"autoprefixer",prepare(_){let ee=loadPrefixes({from:_.opts.from,env:X.env});return{OnceExit(te){timeCapsule(_,ee);if(X.remove!==false){ee.processor.remove(te,_)}if(X.add!==false){ee.processor.add(te,_)}}}},info(_){_=_||{};_.from=_.from||process.cwd();return ae(loadPrefixes(_))},options:X,browsers:_}}plugin.postcss=true;plugin.data=le;plugin.defaults=te.defaults;plugin.info=()=>plugin().info()},3725:_=>{function last(_){return _[_.length-1]}let X={parse(_){let X=[""];let ee=[X];for(let te of _){if(te==="("){X=[""];last(ee).push(X);ee.push(X);continue}if(te===")"){ee.pop();X=last(ee);X.push("");continue}X[X.length-1]+=te}return ee[0]},stringify(_){let ee="";for(let te of _){if(typeof te==="object"){ee+=`(${X.stringify(te)})`;continue}ee+=te}return ee}};_.exports=X},1901:(_,X,ee)=>{let te=ee(4907);let re=ee(9613).agents;let se=ee(4012);class Browsers{static prefixes(){if(this.prefixesCache){return this.prefixesCache}this.prefixesCache=[];for(let _ in re){this.prefixesCache.push(`-${re[_].prefix}-`)}this.prefixesCache=se.uniq(this.prefixesCache).sort(((_,X)=>X.length-_.length));return this.prefixesCache}static withPrefix(_){if(!this.prefixesRegexp){this.prefixesRegexp=new RegExp(this.prefixes().join("|"))}return this.prefixesRegexp.test(_)}constructor(_,X,ee,te){this.data=_;this.options=ee||{};this.browserslistOpts=te||{};this.selected=this.parse(X)}parse(_){let X={};for(let _ in this.browserslistOpts){X[_]=this.browserslistOpts[_]}X.path=this.options.from;return te(_,X)}prefix(_){let[X,ee]=_.split(" ");let te=this.data[X];let re=te.prefix_exceptions&&te.prefix_exceptions[ee];if(!re){re=te.prefix}return`-${re}-`}isSelected(_){return this.selected.includes(_)}}_.exports=Browsers},7251:(_,X,ee)=>{let te=ee(4877);let re=ee(1901);let se=ee(4012);class Declaration extends te{check(){return true}prefixed(_,X){return X+_}normalize(_){return _}otherPrefixes(_,X){for(let ee of re.prefixes()){if(ee===X){continue}if(_.includes(ee)){return true}}return false}set(_,X){_.prop=this.prefixed(_.prop,X);return _}needCascade(_){if(!_._autoprefixerCascade){_._autoprefixerCascade=this.all.options.cascade!==false&&_.raw("before").includes("\n")}return _._autoprefixerCascade}maxPrefixed(_,X){if(X._autoprefixerMax){return X._autoprefixerMax}let ee=0;for(let X of _){X=se.removeNote(X);if(X.length>ee){ee=X.length}}X._autoprefixerMax=ee;return X._autoprefixerMax}calcBefore(_,X,ee=""){let te=this.maxPrefixed(_,X);let re=te-se.removeNote(ee).length;let ne=X.raw("before");if(re>0){ne+=Array(re).fill(" ").join("")}return ne}restoreBefore(_){let X=_.raw("before").split("\n");let ee=X[X.length-1];this.all.group(_).up((_=>{let X=_.raw("before").split("\n");let te=X[X.length-1];if(te.length<ee.length){ee=te}}));X[X.length-1]=ee;_.raws.before=X.join("\n")}insert(_,X,ee){let te=this.set(this.clone(_),X);if(!te)return undefined;let re=_.parent.some((_=>_.prop===te.prop&&_.value===te.value));if(re){return undefined}if(this.needCascade(_)){te.raws.before=this.calcBefore(ee,_,X)}return _.parent.insertBefore(_,te)}isAlready(_,X){let ee=this.all.group(_).up((_=>_.prop===X));if(!ee){ee=this.all.group(_).down((_=>_.prop===X))}return ee}add(_,X,ee,te){let re=this.prefixed(_.prop,X);if(this.isAlready(_,re)||this.otherPrefixes(_.value,X)){return undefined}return this.insert(_,X,ee,te)}process(_,X){if(!this.needCascade(_)){super.process(_,X);return}let ee=super.process(_,X);if(!ee||!ee.length){return}this.restoreBefore(_);_.raws.before=this.calcBefore(ee,_)}old(_,X){return[this.prefixed(_,X)]}}_.exports=Declaration},5832:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class AlignContent extends re{prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2012){return X+"flex-line-pack"}return super.prefixed(_,X)}normalize(){return"align-content"}set(_,X){let ee=te(X)[0];if(ee===2012){_.value=AlignContent.oldValues[_.value]||_.value;return super.set(_,X)}if(ee==="final"){return super.set(_,X)}return undefined}}AlignContent.names=["align-content","flex-line-pack"];AlignContent.oldValues={"flex-end":"end","flex-start":"start","space-between":"justify","space-around":"distribute"};_.exports=AlignContent},5199:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class AlignItems extends re{prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2009){return X+"box-align"}if(ee===2012){return X+"flex-align"}return super.prefixed(_,X)}normalize(){return"align-items"}set(_,X){let ee=te(X)[0];if(ee===2009||ee===2012){_.value=AlignItems.oldValues[_.value]||_.value}return super.set(_,X)}}AlignItems.names=["align-items","flex-align","box-align"];AlignItems.oldValues={"flex-end":"end","flex-start":"start"};_.exports=AlignItems},4741:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class AlignSelf extends re{check(_){return _.parent&&!_.parent.some((_=>_.prop&&_.prop.startsWith("grid-")))}prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2012){return X+"flex-item-align"}return super.prefixed(_,X)}normalize(){return"align-self"}set(_,X){let ee=te(X)[0];if(ee===2012){_.value=AlignSelf.oldValues[_.value]||_.value;return super.set(_,X)}if(ee==="final"){return super.set(_,X)}return undefined}}AlignSelf.names=["align-self","flex-item-align"];AlignSelf.oldValues={"flex-end":"end","flex-start":"start"};_.exports=AlignSelf},2753:(_,X,ee)=>{let te=ee(7251);class Animation extends te{check(_){return!_.value.split(/\s+/).some((_=>{let X=_.toLowerCase();return X==="reverse"||X==="alternate-reverse"}))}}Animation.names=["animation","animation-direction"];_.exports=Animation},9191:(_,X,ee)=>{let te=ee(7251);let re=ee(4012);class Appearance extends te{constructor(_,X,ee){super(_,X,ee);if(this.prefixes){this.prefixes=re.uniq(this.prefixes.map((_=>{if(_==="-ms-"){return"-webkit-"}return _})))}}}Appearance.names=["appearance"];_.exports=Appearance},6564:(_,X,ee)=>{let te=ee(3292);let re=ee(4012);class Autofill extends te{constructor(_,X,ee){super(_,X,ee);if(this.prefixes){this.prefixes=re.uniq(this.prefixes.map((_=>"-webkit-")))}}prefixed(_){if(_==="-webkit-"){return":-webkit-autofill"}return`:${_}autofill`}}Autofill.names=[":autofill"];_.exports=Autofill},1898:(_,X,ee)=>{let te=ee(7251);let re=ee(4012);class BackdropFilter extends te{constructor(_,X,ee){super(_,X,ee);if(this.prefixes){this.prefixes=re.uniq(this.prefixes.map((_=>_==="-ms-"?"-webkit-":_)))}}}BackdropFilter.names=["backdrop-filter"];_.exports=BackdropFilter},758:(_,X,ee)=>{let te=ee(7251);let re=ee(4012);class BackgroundClip extends te{constructor(_,X,ee){super(_,X,ee);if(this.prefixes){this.prefixes=re.uniq(this.prefixes.map((_=>_==="-ms-"?"-webkit-":_)))}}check(_){return _.value.toLowerCase()==="text"}}BackgroundClip.names=["background-clip"];_.exports=BackgroundClip},3177:(_,X,ee)=>{let te=ee(7251);class BackgroundSize extends te{set(_,X){let ee=_.value.toLowerCase();if(X==="-webkit-"&&!ee.includes(" ")&&ee!=="contain"&&ee!=="cover"){_.value=_.value+" "+_.value}return super.set(_,X)}}BackgroundSize.names=["background-size"];_.exports=BackgroundSize},3428:(_,X,ee)=>{let te=ee(7251);class BlockLogical extends te{prefixed(_,X){if(_.includes("-start")){return X+_.replace("-block-start","-before")}return X+_.replace("-block-end","-after")}normalize(_){if(_.includes("-before")){return _.replace("-before","-block-start")}return _.replace("-after","-block-end")}}BlockLogical.names=["border-block-start","border-block-end","margin-block-start","margin-block-end","padding-block-start","padding-block-end","border-before","border-after","margin-before","margin-after","padding-before","padding-after"];_.exports=BlockLogical},8423:(_,X,ee)=>{let te=ee(7251);class BorderImage extends te{set(_,X){_.value=_.value.replace(/\s+fill(\s)/,"$1");return super.set(_,X)}}BorderImage.names=["border-image"];_.exports=BorderImage},9800:(_,X,ee)=>{let te=ee(7251);class BorderRadius extends te{prefixed(_,X){if(X==="-moz-"){return X+(BorderRadius.toMozilla[_]||_)}return super.prefixed(_,X)}normalize(_){return BorderRadius.toNormal[_]||_}}BorderRadius.names=["border-radius"];BorderRadius.toMozilla={};BorderRadius.toNormal={};for(let _ of["top","bottom"]){for(let X of["left","right"]){let ee=`border-${_}-${X}-radius`;let te=`border-radius-${_}${X}`;BorderRadius.names.push(ee);BorderRadius.names.push(te);BorderRadius.toMozilla[ee]=te;BorderRadius.toNormal[te]=ee}}_.exports=BorderRadius},5128:(_,X,ee)=>{let te=ee(7251);class BreakProps extends te{prefixed(_,X){return`${X}column-${_}`}normalize(_){if(_.includes("inside")){return"break-inside"}if(_.includes("before")){return"break-before"}return"break-after"}set(_,X){if(_.prop==="break-inside"&&_.value==="avoid-column"||_.value==="avoid-page"){_.value="avoid"}return super.set(_,X)}insert(_,X,ee){if(_.prop!=="break-inside"){return super.insert(_,X,ee)}if(/region/i.test(_.value)||/page/i.test(_.value)){return undefined}return super.insert(_,X,ee)}}BreakProps.names=["break-inside","page-break-inside","column-break-inside","break-before","page-break-before","column-break-before","break-after","page-break-after","column-break-after"];_.exports=BreakProps},1741:(_,X,ee)=>{let te=ee(7251);class ColorAdjust extends te{prefixed(_,X){return X+"print-color-adjust"}normalize(){return"color-adjust"}}ColorAdjust.names=["color-adjust","print-color-adjust"];_.exports=ColorAdjust},3914:(_,X,ee)=>{let te=ee(977).list;let re=ee(3712);class CrossFade extends re{replace(_,X){return te.space(_).map((_=>{if(_.slice(0,+this.name.length+1)!==this.name+"("){return _}let ee=_.lastIndexOf(")");let te=_.slice(ee+1);let re=_.slice(this.name.length+1,ee);if(X==="-webkit-"){let _=re.match(/\d*.?\d+%?/);if(_){re=re.slice(_[0].length).trim();re+=`, ${_[0]}`}else{re+=", 0.5"}}return X+this.name+"("+re+")"+te})).join(" ")}}CrossFade.names=["cross-fade"];_.exports=CrossFade},2082:(_,X,ee)=>{let te=ee(7582);let re=ee(3660);let se=ee(3712);class DisplayFlex extends se{constructor(_,X){super(_,X);if(_==="display-flex"){this.name="flex"}}check(_){return _.prop==="display"&&_.value===this.name}prefixed(_){let X,ee;[X,_]=te(_);if(X===2009){if(this.name==="flex"){ee="box"}else{ee="inline-box"}}else if(X===2012){if(this.name==="flex"){ee="flexbox"}else{ee="inline-flexbox"}}else if(X==="final"){ee=this.name}return _+ee}replace(_,X){return this.prefixed(X)}old(_){let X=this.prefixed(_);if(!X)return undefined;return new re(this.name,X)}}DisplayFlex.names=["display-flex","inline-flex"];_.exports=DisplayFlex},5067:(_,X,ee)=>{let te=ee(3712);class DisplayGrid extends te{constructor(_,X){super(_,X);if(_==="display-grid"){this.name="grid"}}check(_){return _.prop==="display"&&_.value===this.name}}DisplayGrid.names=["display-grid","inline-grid"];_.exports=DisplayGrid},5277:(_,X,ee)=>{let te=ee(3292);let re=ee(4012);class FileSelectorButton extends te{constructor(_,X,ee){super(_,X,ee);if(this.prefixes){this.prefixes=re.uniq(this.prefixes.map((_=>"-webkit-")))}}prefixed(_){if(_==="-webkit-"){return"::-webkit-file-upload-button"}return`::${_}file-selector-button`}}FileSelectorButton.names=["::file-selector-button"];_.exports=FileSelectorButton},2777:(_,X,ee)=>{let te=ee(3712);class FilterValue extends te{constructor(_,X){super(_,X);if(_==="filter-function"){this.name="filter"}}}FilterValue.names=["filter","filter-function"];_.exports=FilterValue},751:(_,X,ee)=>{let te=ee(7251);class Filter extends te{check(_){let X=_.value;return!X.toLowerCase().includes("alpha(")&&!X.includes("DXImageTransform.Microsoft")&&!X.includes("data:image/svg+xml")}}Filter.names=["filter"];_.exports=Filter},3431:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class FlexBasis extends re{normalize(){return"flex-basis"}prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2012){return X+"flex-preferred-size"}return super.prefixed(_,X)}set(_,X){let ee;[ee,X]=te(X);if(ee===2012||ee==="final"){return super.set(_,X)}return undefined}}FlexBasis.names=["flex-basis","flex-preferred-size"];_.exports=FlexBasis},1445:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class FlexDirection extends re{normalize(){return"flex-direction"}insert(_,X,ee){let re;[re,X]=te(X);if(re!==2009){return super.insert(_,X,ee)}let se=_.parent.some((_=>_.prop===X+"box-orient"||_.prop===X+"box-direction"));if(se){return undefined}let ne=_.value;let ie,oe;if(ne==="inherit"||ne==="initial"||ne==="unset"){ie=ne;oe=ne}else{ie=ne.includes("row")?"horizontal":"vertical";oe=ne.includes("reverse")?"reverse":"normal"}let ae=this.clone(_);ae.prop=X+"box-orient";ae.value=ie;if(this.needCascade(_)){ae.raws.before=this.calcBefore(ee,_,X)}_.parent.insertBefore(_,ae);ae=this.clone(_);ae.prop=X+"box-direction";ae.value=oe;if(this.needCascade(_)){ae.raws.before=this.calcBefore(ee,_,X)}return _.parent.insertBefore(_,ae)}old(_,X){let ee;[ee,X]=te(X);if(ee===2009){return[X+"box-orient",X+"box-direction"]}else{return super.old(_,X)}}}FlexDirection.names=["flex-direction","box-direction","box-orient"];_.exports=FlexDirection},4288:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class FlexFlow extends re{insert(_,X,ee){let re;[re,X]=te(X);if(re!==2009){return super.insert(_,X,ee)}let se=_.value.split(/\s+/).filter((_=>_!=="wrap"&&_!=="nowrap"&&"wrap-reverse"));if(se.length===0){return undefined}let ne=_.parent.some((_=>_.prop===X+"box-orient"||_.prop===X+"box-direction"));if(ne){return undefined}let ie=se[0];let oe=ie.includes("row")?"horizontal":"vertical";let ae=ie.includes("reverse")?"reverse":"normal";let le=this.clone(_);le.prop=X+"box-orient";le.value=oe;if(this.needCascade(_)){le.raws.before=this.calcBefore(ee,_,X)}_.parent.insertBefore(_,le);le=this.clone(_);le.prop=X+"box-direction";le.value=ae;if(this.needCascade(_)){le.raws.before=this.calcBefore(ee,_,X)}return _.parent.insertBefore(_,le)}}FlexFlow.names=["flex-flow","box-direction","box-orient"];_.exports=FlexFlow},553:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class Flex extends re{normalize(){return"flex"}prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2009){return X+"box-flex"}if(ee===2012){return X+"flex-positive"}return super.prefixed(_,X)}}Flex.names=["flex-grow","flex-positive"];_.exports=Flex},4274:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class FlexShrink extends re{normalize(){return"flex-shrink"}prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2012){return X+"flex-negative"}return super.prefixed(_,X)}set(_,X){let ee;[ee,X]=te(X);if(ee===2012||ee==="final"){return super.set(_,X)}return undefined}}FlexShrink.names=["flex-shrink","flex-negative"];_.exports=FlexShrink},7582:_=>{_.exports=function(_){let X;if(_==="-webkit- 2009"||_==="-moz-"){X=2009}else if(_==="-ms-"){X=2012}else if(_==="-webkit-"){X="final"}if(_==="-webkit- 2009"){_="-webkit-"}return[X,_]}},7252:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class FlexWrap extends re{set(_,X){let ee=te(X)[0];if(ee!==2009){return super.set(_,X)}return undefined}}FlexWrap.names=["flex-wrap"];_.exports=FlexWrap},9332:(_,X,ee)=>{let te=ee(977).list;let re=ee(7582);let se=ee(7251);class Flex extends se{prefixed(_,X){let ee;[ee,X]=re(X);if(ee===2009){return X+"box-flex"}return super.prefixed(_,X)}normalize(){return"flex"}set(_,X){let ee=re(X)[0];if(ee===2009){_.value=te.space(_.value)[0];_.value=Flex.oldValues[_.value]||_.value;return super.set(_,X)}if(ee===2012){let X=te.space(_.value);if(X.length===3&&X[2]==="0"){_.value=X.slice(0,2).concat("0px").join(" ")}}return super.set(_,X)}}Flex.names=["flex","box-flex"];Flex.oldValues={auto:"1",none:"0"};_.exports=Flex},8545:(_,X,ee)=>{let te=ee(3292);class Fullscreen extends te{prefixed(_){if(_==="-webkit-"){return":-webkit-full-screen"}if(_==="-moz-"){return":-moz-full-screen"}return`:${_}fullscreen`}}Fullscreen.names=[":fullscreen"];_.exports=Fullscreen},7222:(_,X,ee)=>{let te=ee(2045);let re=ee(2443);let se=ee(3660);let ne=ee(3712);let ie=ee(4012);let oe=/top|left|right|bottom/gi;class Gradient extends ne{replace(_,X){let ee=te(_);for(let _ of ee.nodes){if(_.type==="function"&&_.value===this.name){_.nodes=this.newDirection(_.nodes);_.nodes=this.normalize(_.nodes);if(X==="-webkit- old"){let X=this.oldWebkit(_);if(!X){return false}}else{_.nodes=this.convertDirection(_.nodes);_.value=X+_.value}}}return ee.toString()}replaceFirst(_,...X){let ee=X.map((_=>{if(_===" "){return{type:"space",value:_}}return{type:"word",value:_}}));return ee.concat(_.slice(1))}normalizeUnit(_,X){let ee=parseFloat(_);let te=ee/X*360;return`${te}deg`}normalize(_){if(!_[0])return _;if(/-?\d+(.\d+)?grad/.test(_[0].value)){_[0].value=this.normalizeUnit(_[0].value,400)}else if(/-?\d+(.\d+)?rad/.test(_[0].value)){_[0].value=this.normalizeUnit(_[0].value,2*Math.PI)}else if(/-?\d+(.\d+)?turn/.test(_[0].value)){_[0].value=this.normalizeUnit(_[0].value,1)}else if(_[0].value.includes("deg")){let X=parseFloat(_[0].value);X=re.wrap(0,360,X);_[0].value=`${X}deg`}if(_[0].value==="0deg"){_=this.replaceFirst(_,"to"," ","top")}else if(_[0].value==="90deg"){_=this.replaceFirst(_,"to"," ","right")}else if(_[0].value==="180deg"){_=this.replaceFirst(_,"to"," ","bottom")}else if(_[0].value==="270deg"){_=this.replaceFirst(_,"to"," ","left")}return _}newDirection(_){if(_[0].value==="to"){return _}oe.lastIndex=0;if(!oe.test(_[0].value)){return _}_.unshift({type:"word",value:"to"},{type:"space",value:" "});for(let X=2;X<_.length;X++){if(_[X].type==="div"){break}if(_[X].type==="word"){_[X].value=this.revertDirection(_[X].value)}}return _}isRadial(_){let X="before";for(let ee of _){if(X==="before"&&ee.type==="space"){X="at"}else if(X==="at"&&ee.value==="at"){X="after"}else if(X==="after"&&ee.type==="space"){return true}else if(ee.type==="div"){break}else{X="before"}}return false}convertDirection(_){if(_.length>0){if(_[0].value==="to"){this.fixDirection(_)}else if(_[0].value.includes("deg")){this.fixAngle(_)}else if(this.isRadial(_)){this.fixRadial(_)}}return _}fixDirection(_){_.splice(0,2);for(let X of _){if(X.type==="div"){break}if(X.type==="word"){X.value=this.revertDirection(X.value)}}}fixAngle(_){let X=_[0].value;X=parseFloat(X);X=Math.abs(450-X)%360;X=this.roundFloat(X,3);_[0].value=`${X}deg`}fixRadial(_){let X=[];let ee=[];let te,re,se,ne,ie;for(ne=0;ne<_.length-2;ne++){te=_[ne];re=_[ne+1];se=_[ne+2];if(te.type==="space"&&re.value==="at"&&se.type==="space"){ie=ne+3;break}else{X.push(te)}}let oe;for(ne=ie;ne<_.length;ne++){if(_[ne].type==="div"){oe=_[ne];break}else{ee.push(_[ne])}}_.splice(0,ne,...ee,oe,...X)}revertDirection(_){return Gradient.directions[_.toLowerCase()]||_}roundFloat(_,X){return parseFloat(_.toFixed(X))}oldWebkit(_){let{nodes:X}=_;let ee=te.stringify(_.nodes);if(this.name!=="linear-gradient"){return false}if(X[0]&&X[0].value.includes("deg")){return false}if(ee.includes("px")||ee.includes("-corner")||ee.includes("-side")){return false}let re=[[]];for(let _ of X){re[re.length-1].push(_);if(_.type==="div"&&_.value===","){re.push([])}}this.oldDirection(re);this.colorStops(re);_.nodes=[];for(let X of re){_.nodes=_.nodes.concat(X)}_.nodes.unshift({type:"word",value:"linear"},this.cloneDiv(_.nodes));_.value="-webkit-gradient";return true}oldDirection(_){let X=this.cloneDiv(_[0]);if(_[0][0].value!=="to"){return _.unshift([{type:"word",value:Gradient.oldDirections.bottom},X])}else{let ee=[];for(let X of _[0].slice(2)){if(X.type==="word"){ee.push(X.value.toLowerCase())}}ee=ee.join(" ");let te=Gradient.oldDirections[ee]||ee;_[0]=[{type:"word",value:te},X];return _[0]}}cloneDiv(_){for(let X of _){if(X.type==="div"&&X.value===","){return X}}return{type:"div",value:",",after:" "}}colorStops(_){let X=[];for(let ee=0;ee<_.length;ee++){let re;let se=_[ee];let ne;if(ee===0){continue}let ie=te.stringify(se[0]);if(se[1]&&se[1].type==="word"){re=se[1].value}else if(se[2]&&se[2].type==="word"){re=se[2].value}let oe;if(ee===1&&(!re||re==="0%")){oe=`from(${ie})`}else if(ee===_.length-1&&(!re||re==="100%")){oe=`to(${ie})`}else if(re){oe=`color-stop(${re}, ${ie})`}else{oe=`color-stop(${ie})`}let ae=se[se.length-1];_[ee]=[{type:"word",value:oe}];if(ae.type==="div"&&ae.value===","){ne=_[ee].push(ae)}X.push(ne)}return X}old(_){if(_==="-webkit-"){let X=this.name==="linear-gradient"?"linear":"radial";let ee="-gradient";let te=ie.regexp(`-webkit-(${X}-gradient|gradient\\(\\s*${X})`,false);return new se(this.name,_+this.name,ee,te)}else{return super.old(_)}}add(_,X){let ee=_.prop;if(ee.includes("mask")){if(X==="-webkit-"||X==="-webkit- old"){return super.add(_,X)}}else if(ee==="list-style"||ee==="list-style-image"||ee==="content"){if(X==="-webkit-"||X==="-webkit- old"){return super.add(_,X)}}else{return super.add(_,X)}return undefined}}Gradient.names=["linear-gradient","repeating-linear-gradient","radial-gradient","repeating-radial-gradient"];Gradient.directions={top:"bottom",left:"right",bottom:"top",right:"left"};Gradient.oldDirections={top:"left bottom, left top",left:"right top, left top",bottom:"left top, left bottom",right:"left top, right top","top right":"left bottom, right top","top left":"right bottom, left top","right top":"left bottom, right top","right bottom":"left top, right bottom","bottom right":"left top, right bottom","bottom left":"right top, left bottom","left top":"right bottom, left top","left bottom":"right top, left bottom"};_.exports=Gradient},4163:(_,X,ee)=>{let te=ee(7251);let re=ee(2671);class GridArea extends te{insert(_,X,ee,te){if(X!=="-ms-")return super.insert(_,X,ee);let se=re.parse(_);let[ne,ie]=re.translate(se,0,2);let[oe,ae]=re.translate(se,1,3);[["grid-row",ne],["grid-row-span",ie],["grid-column",oe],["grid-column-span",ae]].forEach((([X,ee])=>{re.insertDecl(_,X,ee)}));re.warnTemplateSelectorNotFound(_,te);re.warnIfGridRowColumnExists(_,te);return undefined}}GridArea.names=["grid-area"];_.exports=GridArea},7027:(_,X,ee)=>{let te=ee(7251);class GridColumnAlign extends te{check(_){return!_.value.includes("flex-")&&_.value!=="baseline"}prefixed(_,X){return X+"grid-column-align"}normalize(){return"justify-self"}}GridColumnAlign.names=["grid-column-align"];_.exports=GridColumnAlign},866:(_,X,ee)=>{let te=ee(7251);class GridEnd extends te{insert(_,X,ee,te){if(X!=="-ms-")return super.insert(_,X,ee);let re=this.clone(_);let se=_.prop.replace(/end$/,"start");let ne=X+_.prop.replace(/end$/,"span");if(_.parent.some((_=>_.prop===ne))){return undefined}re.prop=ne;if(_.value.includes("span")){re.value=_.value.replace(/span\s/i,"")}else{let X;_.parent.walkDecls(se,(_=>{X=_}));if(X){let ee=Number(_.value)-Number(X.value)+"";re.value=ee}else{_.warn(te,`Can not prefix ${_.prop} (${se} is not found)`)}}_.cloneBefore(re);return undefined}}GridEnd.names=["grid-row-end","grid-column-end"];_.exports=GridEnd},8605:(_,X,ee)=>{let te=ee(7251);class GridRowAlign extends te{check(_){return!_.value.includes("flex-")&&_.value!=="baseline"}prefixed(_,X){return X+"grid-row-align"}normalize(){return"align-self"}}GridRowAlign.names=["grid-row-align"];_.exports=GridRowAlign},4917:(_,X,ee)=>{let te=ee(7251);let re=ee(2671);class GridRowColumn extends te{insert(_,X,ee){if(X!=="-ms-")return super.insert(_,X,ee);let te=re.parse(_);let[se,ne]=re.translate(te,0,1);let ie=te[0]&&te[0].includes("span");if(ie){ne=te[0].join("").replace(/\D/g,"")}[[_.prop,se],[`${_.prop}-span`,ne]].forEach((([X,ee])=>{re.insertDecl(_,X,ee)}));return undefined}}GridRowColumn.names=["grid-row","grid-column"];_.exports=GridRowColumn},5826:(_,X,ee)=>{let te=ee(7251);let{prefixTrackProp:re,prefixTrackValue:se,autoplaceGridItems:ne,getGridGap:ie,inheritGridGap:oe}=ee(2671);let ae=ee(8243);class GridRowsColumns extends te{prefixed(_,X){if(X==="-ms-"){return re({prop:_,prefix:X})}return super.prefixed(_,X)}normalize(_){return _.replace(/^grid-(rows|columns)/,"grid-template-$1")}insert(_,X,ee,te){if(X!=="-ms-")return super.insert(_,X,ee);let{parent:le,prop:ue,value:ce}=_;let pe=ue.includes("rows");let fe=ue.includes("columns");let de=le.some((_=>_.prop==="grid-template"||_.prop==="grid-template-areas"));if(de&&pe){return false}let he=new ae({options:{}});let me=he.gridStatus(le,te);let ge=ie(_);ge=oe(_,ge)||ge;let be=pe?ge.row:ge.column;if((me==="no-autoplace"||me===true)&&!de){be=null}let ve=se({value:ce,gap:be});_.cloneBefore({prop:re({prop:ue,prefix:X}),value:ve});let ye=le.nodes.find((_=>_.prop==="grid-auto-flow"));let we="row";if(ye&&!he.disabled(ye,te)){we=ye.value.trim()}if(me==="autoplace"){let X=le.nodes.find((_=>_.prop==="grid-template-rows"));if(!X&&de){return undefined}else if(!X&&!de){_.warn(te,"Autoplacement does not work without grid-template-rows property");return undefined}let ee=le.nodes.find((_=>_.prop==="grid-template-columns"));if(!ee&&!de){_.warn(te,"Autoplacement does not work without grid-template-columns property")}if(fe&&!de){ne(_,te,ge,we)}}return undefined}}GridRowsColumns.names=["grid-template-rows","grid-template-columns","grid-rows","grid-columns"];_.exports=GridRowsColumns},8707:(_,X,ee)=>{let te=ee(7251);class GridStart extends te{check(_){let X=_.value;return!X.includes("/")||X.includes("span")}normalize(_){return _.replace("-start","")}prefixed(_,X){let ee=super.prefixed(_,X);if(X==="-ms-"){ee=ee.replace("-start","")}return ee}}GridStart.names=["grid-row-start","grid-column-start"];_.exports=GridStart},8881:(_,X,ee)=>{let te=ee(7251);let{parseGridAreas:re,warnMissedAreas:se,prefixTrackProp:ne,prefixTrackValue:ie,getGridGap:oe,warnGridGap:ae,inheritGridGap:le}=ee(2671);function getGridRows(_){return _.trim().slice(1,-1).split(/["']\s*["']?/g)}class GridTemplateAreas extends te{insert(_,X,ee,te){if(X!=="-ms-")return super.insert(_,X,ee);let ue=false;let ce=false;let pe=_.parent;let fe=oe(_);fe=le(_,fe)||fe;pe.walkDecls(/-ms-grid-rows/,(_=>_.remove()));pe.walkDecls(/grid-template-(rows|columns)/,(_=>{if(_.prop==="grid-template-rows"){ce=true;let{prop:ee,value:te}=_;_.cloneBefore({prop:ne({prop:ee,prefix:X}),value:ie({value:te,gap:fe.row})})}else{ue=true}}));let de=getGridRows(_.value);if(ue&&!ce&&fe.row&&de.length>1){_.cloneBefore({prop:"-ms-grid-rows",value:ie({value:`repeat(${de.length}, auto)`,gap:fe.row}),raws:{}})}ae({gap:fe,hasColumns:ue,decl:_,result:te});let he=re({rows:de,gap:fe});se(he,_,te);return _}}GridTemplateAreas.names=["grid-template-areas"];_.exports=GridTemplateAreas},1006:(_,X,ee)=>{let te=ee(7251);let{parseTemplate:re,warnMissedAreas:se,getGridGap:ne,warnGridGap:ie,inheritGridGap:oe}=ee(2671);class GridTemplate extends te{insert(_,X,ee,te){if(X!=="-ms-")return super.insert(_,X,ee);if(_.parent.some((_=>_.prop==="-ms-grid-rows"))){return undefined}let ae=ne(_);let le=oe(_,ae);let{rows:ue,columns:ce,areas:pe}=re({decl:_,gap:le||ae});let fe=Object.keys(pe).length>0;let de=Boolean(ue);let he=Boolean(ce);ie({gap:ae,hasColumns:he,decl:_,result:te});se(pe,_,te);if(de&&he||fe){_.cloneBefore({prop:"-ms-grid-rows",value:ue,raws:{}})}if(he){_.cloneBefore({prop:"-ms-grid-columns",value:ce,raws:{}})}return _}}GridTemplate.names=["grid-template"];_.exports=GridTemplate},2671:(_,X,ee)=>{let te=ee(2045);let re=ee(977).list;let se=ee(4012).uniq;let ne=ee(4012).escapeRegexp;let ie=ee(4012).splitSelector;function convert(_){if(_&&_.length===2&&_[0]==="span"&&parseInt(_[1],10)>0){return[false,parseInt(_[1],10)]}if(_&&_.length===1&&parseInt(_[0],10)>0){return[parseInt(_[0],10),false]}return[false,false]}X.translate=translate;function translate(_,X,ee){let te=_[X];let re=_[ee];if(!te){return[false,false]}let[se,ne]=convert(te);let[ie,oe]=convert(re);if(se&&!re){return[se,false]}if(ne&&ie){return[ie-ne,ne]}if(se&&oe){return[se,oe]}if(se&&ie){return[se,ie-se]}return[false,false]}X.parse=parse;function parse(_){let X=te(_.value);let ee=[];let re=0;ee[re]=[];for(let _ of X.nodes){if(_.type==="div"){re+=1;ee[re]=[]}else if(_.type==="word"){ee[re].push(_.value)}}return ee}X.insertDecl=insertDecl;function insertDecl(_,X,ee){if(ee&&!_.parent.some((_=>_.prop===`-ms-${X}`))){_.cloneBefore({prop:`-ms-${X}`,value:ee.toString()})}}X.prefixTrackProp=prefixTrackProp;function prefixTrackProp({prop:_,prefix:X}){return X+_.replace("template-","")}function transformRepeat({nodes:_},{gap:X}){let{count:ee,size:re}=_.reduce(((_,X)=>{if(X.type==="div"&&X.value===","){_.key="size"}else{_[_.key].push(te.stringify(X))}return _}),{key:"count",size:[],count:[]});if(X){re=re.filter((_=>_.trim()));let _=[];for(let te=1;te<=ee;te++){re.forEach(((ee,re)=>{if(re>0||te>1){_.push(X)}_.push(ee)}))}return _.join(" ")}return`(${re.join("")})[${ee.join("")}]`}X.prefixTrackValue=prefixTrackValue;function prefixTrackValue({value:_,gap:X}){let ee=te(_).nodes.reduce(((_,ee)=>{if(ee.type==="function"&&ee.value==="repeat"){return _.concat({type:"word",value:transformRepeat(ee,{gap:X})})}if(X&&ee.type==="space"){return _.concat({type:"space",value:" "},{type:"word",value:X},ee)}return _.concat(ee)}),[]);return te.stringify(ee)}let oe=/^\.+$/;function track(_,X){return{start:_,end:X,span:X-_}}function getColumns(_){return _.trim().split(/\s+/g)}X.parseGridAreas=parseGridAreas;function parseGridAreas({rows:_,gap:X}){return _.reduce(((_,ee,te)=>{if(X.row)te*=2;if(ee.trim()==="")return _;getColumns(ee).forEach(((ee,re)=>{if(oe.test(ee))return;if(X.column)re*=2;if(typeof _[ee]==="undefined"){_[ee]={column:track(re+1,re+2),row:track(te+1,te+2)}}else{let{column:X,row:se}=_[ee];X.start=Math.min(X.start,re+1);X.end=Math.max(X.end,re+2);X.span=X.end-X.start;se.start=Math.min(se.start,te+1);se.end=Math.max(se.end,te+2);se.span=se.end-se.start}}));return _}),{})}function testTrack(_){return _.type==="word"&&/^\[.+]$/.test(_.value)}function verifyRowSize(_){if(_.areas.length>_.rows.length){_.rows.push("auto")}return _}X.parseTemplate=parseTemplate;function parseTemplate({decl:_,gap:X}){let ee=te(_.value).nodes.reduce(((_,X)=>{let{type:ee,value:re}=X;if(testTrack(X)||ee==="space")return _;if(ee==="string"){_=verifyRowSize(_);_.areas.push(re)}if(ee==="word"||ee==="function"){_[_.key].push(te.stringify(X))}if(ee==="div"&&re==="/"){_.key="columns";_=verifyRowSize(_)}return _}),{key:"rows",columns:[],rows:[],areas:[]});return{areas:parseGridAreas({rows:ee.areas,gap:X}),columns:prefixTrackValue({value:ee.columns.join(" "),gap:X.column}),rows:prefixTrackValue({value:ee.rows.join(" "),gap:X.row})}}function getMSDecls(_,X=false,ee=false){let te=[{prop:"-ms-grid-row",value:String(_.row.start)}];if(_.row.span>1||X){te.push({prop:"-ms-grid-row-span",value:String(_.row.span)})}te.push({prop:"-ms-grid-column",value:String(_.column.start)});if(_.column.span>1||ee){te.push({prop:"-ms-grid-column-span",value:String(_.column.span)})}return te}function getParentMedia(_){if(_.type==="atrule"&&_.name==="media"){return _}if(!_.parent){return false}return getParentMedia(_.parent)}function changeDuplicateAreaSelectors(_,X){_=_.map((_=>{let X=re.space(_);let ee=re.comma(_);if(X.length>ee.length){_=X.slice(-1).join("")}return _}));return _.map((_=>{let ee=X.map(((X,ee)=>{let te=ee===0?"":" ";return`${te}${X} > ${_}`}));return ee}))}function selectorsEqual(_,X){return _.selectors.some((_=>X.selectors.includes(_)))}function parseGridTemplatesData(_){let X=[];_.walkDecls(/grid-template(-areas)?$/,(_=>{let ee=_.parent;let te=getParentMedia(ee);let re=getGridGap(_);let ne=inheritGridGap(_,re);let{areas:ie}=parseTemplate({decl:_,gap:ne||re});let oe=Object.keys(ie);if(oe.length===0){return true}let ae=X.reduce(((_,{allAreas:X},ee)=>{let te=X&&oe.some((_=>X.includes(_)));return te?ee:_}),null);if(ae!==null){let{allAreas:_,rules:re}=X[ae];let ne=re.some((_=>_.hasDuplicates===false&&selectorsEqual(_,ee)));let le=false;let ue=re.reduce(((_,X)=>{if(!X.params&&selectorsEqual(X,ee)){le=true;return X.duplicateAreaNames}if(!le){oe.forEach((ee=>{if(X.areas[ee]){_.push(ee)}}))}return se(_)}),[]);re.forEach((_=>{oe.forEach((X=>{let ee=_.areas[X];if(ee&&ee.row.span!==ie[X].row.span){ie[X].row.updateSpan=true}if(ee&&ee.column.span!==ie[X].column.span){ie[X].column.updateSpan=true}}))}));X[ae].allAreas=se([..._,...oe]);X[ae].rules.push({hasDuplicates:!ne,params:te.params,selectors:ee.selectors,node:ee,duplicateAreaNames:ue,areas:ie})}else{X.push({allAreas:oe,areasCount:0,rules:[{hasDuplicates:false,duplicateRules:[],params:te.params,selectors:ee.selectors,node:ee,duplicateAreaNames:[],areas:ie}]})}return undefined}));return X}X.insertAreas=insertAreas;function insertAreas(_,X){let ee=parseGridTemplatesData(_);if(ee.length===0){return undefined}let te={};_.walkDecls("grid-area",(se=>{let ne=se.parent;let ie=ne.first.prop==="-ms-grid-row";let oe=getParentMedia(ne);if(X(se)){return undefined}let ae=_.index(oe||ne);let le=se.value;let ue=ee.filter((_=>_.allAreas.includes(le)))[0];if(!ue){return true}let ce=ue.allAreas[ue.allAreas.length-1];let pe=re.space(ne.selector);let fe=re.comma(ne.selector);let de=pe.length>1&&pe.length>fe.length;if(ie){return false}if(!te[ce]){te[ce]={}}let he=false;for(let X of ue.rules){let ee=X.areas[le];let re=X.duplicateAreaNames.includes(le);if(!ee){let X=te[ce].lastRule;let ee;if(X){ee=_.index(X)}else{ee=-1}if(ae>ee){te[ce].lastRule=oe||ne}continue}if(X.params&&!te[ce][X.params]){te[ce][X.params]=[]}if((!X.hasDuplicates||!re)&&!X.params){getMSDecls(ee,false,false).reverse().forEach((_=>ne.prepend(Object.assign(_,{raws:{between:se.raws.between}}))));te[ce].lastRule=ne;he=true}else if(X.hasDuplicates&&!X.params&&!de){let _=ne.clone();_.removeAll();getMSDecls(ee,ee.row.updateSpan,ee.column.updateSpan).reverse().forEach((X=>_.prepend(Object.assign(X,{raws:{between:se.raws.between}}))));_.selectors=changeDuplicateAreaSelectors(_.selectors,X.selectors);if(te[ce].lastRule){te[ce].lastRule.after(_)}te[ce].lastRule=_;he=true}else if(X.hasDuplicates&&!X.params&&de&&ne.selector.includes(X.selectors[0])){ne.walkDecls(/-ms-grid-(row|column)/,(_=>_.remove()));getMSDecls(ee,ee.row.updateSpan,ee.column.updateSpan).reverse().forEach((_=>ne.prepend(Object.assign(_,{raws:{between:se.raws.between}}))))}else if(X.params){let ie=ne.clone();ie.removeAll();getMSDecls(ee,ee.row.updateSpan,ee.column.updateSpan).reverse().forEach((_=>ie.prepend(Object.assign(_,{raws:{between:se.raws.between}}))));if(X.hasDuplicates&&re){ie.selectors=changeDuplicateAreaSelectors(ie.selectors,X.selectors)}ie.raws=X.node.raws;if(_.index(X.node.parent)>ae){X.node.parent.append(ie)}else{te[ce][X.params].push(ie)}if(!he){te[ce].lastRule=oe||ne}}}return undefined}));Object.keys(te).forEach((_=>{let X=te[_];let ee=X.lastRule;Object.keys(X).reverse().filter((_=>_!=="lastRule")).forEach((_=>{if(X[_].length>0&&ee){ee.after({name:"media",params:_});ee.next().append(X[_])}}))}));return undefined}X.warnMissedAreas=warnMissedAreas;function warnMissedAreas(_,X,ee){let te=Object.keys(_);X.root().walkDecls("grid-area",(_=>{te=te.filter((X=>X!==_.value))}));if(te.length>0){X.warn(ee,"Can not find grid areas: "+te.join(", "))}return undefined}X.warnTemplateSelectorNotFound=warnTemplateSelectorNotFound;function warnTemplateSelectorNotFound(_,X){let ee=_.parent;let te=_.root();let se=false;let ne=re.space(ee.selector).filter((_=>_!==">")).slice(0,-1);if(ne.length>0){let ee=false;let ie=null;te.walkDecls(/grid-template(-areas)?$/,(X=>{let te=X.parent;let oe=te.selectors;let{areas:ae}=parseTemplate({decl:X,gap:getGridGap(X)});let le=ae[_.value];for(let _ of oe){if(ee){break}let X=re.space(_).filter((_=>_!==">"));ee=X.every(((_,X)=>_===ne[X]))}if(ee||!le){return true}if(!ie){ie=te.selector}if(ie&&ie!==te.selector){se=true}return undefined}));if(!ee&&se){_.warn(X,"Autoprefixer cannot find a grid-template "+`containing the duplicate grid-area "${_.value}" `+`with full selector matching: ${ne.join(" ")}`)}}}X.warnIfGridRowColumnExists=warnIfGridRowColumnExists;function warnIfGridRowColumnExists(_,X){let ee=_.parent;let te=[];ee.walkDecls(/^grid-(row|column)/,(_=>{if(!_.prop.endsWith("-end")&&!_.value.startsWith("span")&&!_.prop.endsWith("-gap")){te.push(_)}}));if(te.length>0){te.forEach((_=>{_.warn(X,"You already have a grid-area declaration present in the rule. "+`You should use either grid-area or ${_.prop}, not both`)}))}return undefined}X.getGridGap=getGridGap;function getGridGap(_){let X={};let ee=/^(grid-)?((row|column)-)?gap$/;_.parent.walkDecls(ee,(({prop:_,value:ee})=>{if(/^(grid-)?gap$/.test(_)){let[_,,re]=te(ee).nodes;X.row=_&&te.stringify(_);X.column=re?te.stringify(re):X.row}if(/^(grid-)?row-gap$/.test(_))X.row=ee;if(/^(grid-)?column-gap$/.test(_))X.column=ee}));return X}function parseMediaParams(_){if(!_){return[]}let X=te(_);let ee;let re;X.walk((_=>{if(_.type==="word"&&/min|max/g.test(_.value)){ee=_.value}else if(_.value.includes("px")){re=parseInt(_.value.replace(/\D/g,""))}}));return[ee,re]}function shouldInheritGap(_,X){let ee;let te=ie(_);let re=ie(X);if(te[0].length<re[0].length){return false}else if(te[0].length>re[0].length){let _=te[0].reduce(((_,[X],ee)=>{let te=re[0][0][0];if(X===te){return ee}return false}),false);if(_){ee=re[0].every(((X,ee)=>X.every(((X,re)=>te[0].slice(_)[ee][re]===X))))}}else{ee=re.some((_=>_.every(((_,X)=>_.every(((_,ee)=>te[0][X][ee]===_))))))}return ee}X.inheritGridGap=inheritGridGap;function inheritGridGap(_,X){let ee=_.parent;let te=getParentMedia(ee);let re=ee.root();let se=ie(ee.selector);if(Object.keys(X).length>0){return false}let[oe]=parseMediaParams(te.params);let ae=se[0];let le=ne(ae[ae.length-1][0]);let ue=new RegExp(`(${le}$)|(${le}[,.])`);let ce;re.walkRules(ue,(_=>{let X;if(ee.toString()===_.toString()){return false}_.walkDecls("grid-gap",(_=>X=getGridGap(_)));if(!X||Object.keys(X).length===0){return true}if(!shouldInheritGap(ee.selector,_.selector)){return true}let te=getParentMedia(_);if(te){let _=parseMediaParams(te.params)[0];if(_===oe){ce=X;return true}}else{ce=X;return true}return undefined}));if(ce&&Object.keys(ce).length>0){return ce}return false}X.warnGridGap=warnGridGap;function warnGridGap({gap:_,hasColumns:X,decl:ee,result:te}){let re=_.row&&_.column;if(!X&&(re||_.column&&!_.row)){delete _.column;ee.warn(te,"Can not implement grid-gap without grid-template-columns")}}function normalizeRowColumn(_){let X=te(_).nodes.reduce(((_,X)=>{if(X.type==="function"&&X.value==="repeat"){let ee="count";let[re,se]=X.nodes.reduce(((_,X)=>{if(X.type==="word"&&ee==="count"){_[0]=Math.abs(parseInt(X.value));return _}if(X.type==="div"&&X.value===","){ee="value";return _}if(ee==="value"){_[1]+=te.stringify(X)}return _}),[0,""]);if(re){for(let X=0;X<re;X++){_.push(se)}}return _}if(X.type==="space"){return _}return _.concat(te.stringify(X))}),[]);return X}X.autoplaceGridItems=autoplaceGridItems;function autoplaceGridItems(_,X,ee,te="row"){let{parent:re}=_;let se=re.nodes.find((_=>_.prop==="grid-template-rows"));let ne=normalizeRowColumn(se.value);let ie=normalizeRowColumn(_.value);let oe=ne.map(((_,X)=>Array.from({length:ie.length},((_,ee)=>ee+X*ie.length+1)).join(" ")));let ae=parseGridAreas({rows:oe,gap:ee});let le=Object.keys(ae);let ue=le.map((_=>ae[_]));if(te.includes("column")){ue=ue.sort(((_,X)=>_.column.start-X.column.start))}ue.reverse().forEach(((_,X)=>{let{column:ee,row:te}=_;let se=re.selectors.map((_=>_+` > *:nth-child(${le.length-X})`)).join(", ");let ne=re.clone().removeAll();ne.selector=se;ne.append({prop:"-ms-grid-row",value:te.start});ne.append({prop:"-ms-grid-column",value:ee.start});re.after(ne)}));return undefined}},7084:(_,X,ee)=>{let te=ee(7251);class ImageRendering extends te{check(_){return _.value==="pixelated"}prefixed(_,X){if(X==="-ms-"){return"-ms-interpolation-mode"}return super.prefixed(_,X)}set(_,X){if(X!=="-ms-")return super.set(_,X);_.prop="-ms-interpolation-mode";_.value="nearest-neighbor";return _}normalize(){return"image-rendering"}process(_,X){return super.process(_,X)}}ImageRendering.names=["image-rendering","interpolation-mode"];_.exports=ImageRendering},7541:(_,X,ee)=>{let te=ee(3712);class ImageSet extends te{replace(_,X){let ee=super.replace(_,X);if(X==="-webkit-"){ee=ee.replace(/("[^"]+"|'[^']+')(\s+\d+\w)/gi,"url($1)$2")}return ee}}ImageSet.names=["image-set"];_.exports=ImageSet},4408:(_,X,ee)=>{let te=ee(7251);class InlineLogical extends te{prefixed(_,X){return X+_.replace("-inline","")}normalize(_){return _.replace(/(margin|padding|border)-(start|end)/,"$1-inline-$2")}}InlineLogical.names=["border-inline-start","border-inline-end","margin-inline-start","margin-inline-end","padding-inline-start","padding-inline-end","border-start","border-end","margin-start","margin-end","padding-start","padding-end"];_.exports=InlineLogical},9177:(_,X,ee)=>{let te=ee(3660);let re=ee(3712);function regexp(_){return new RegExp(`(^|[\\s,(])(${_}($|[\\s),]))`,"gi")}class Intrinsic extends re{regexp(){if(!this.regexpCache)this.regexpCache=regexp(this.name);return this.regexpCache}isStretch(){return this.name==="stretch"||this.name==="fill"||this.name==="fill-available"}replace(_,X){if(X==="-moz-"&&this.isStretch()){return _.replace(this.regexp(),"$1-moz-available$3")}if(X==="-webkit-"&&this.isStretch()){return _.replace(this.regexp(),"$1-webkit-fill-available$3")}return super.replace(_,X)}old(_){let X=_+this.name;if(this.isStretch()){if(_==="-moz-"){X="-moz-available"}else if(_==="-webkit-"){X="-webkit-fill-available"}}return new te(this.name,X,X,regexp(X))}add(_,X){if(_.prop.includes("grid")&&X!=="-webkit-"){return undefined}return super.add(_,X)}}Intrinsic.names=["max-content","min-content","fit-content","fill","fill-available","stretch"];_.exports=Intrinsic},2009:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class JustifyContent extends re{prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2009){return X+"box-pack"}if(ee===2012){return X+"flex-pack"}return super.prefixed(_,X)}normalize(){return"justify-content"}set(_,X){let ee=te(X)[0];if(ee===2009||ee===2012){let te=JustifyContent.oldValues[_.value]||_.value;_.value=te;if(ee!==2009||te!=="distribute"){return super.set(_,X)}}else if(ee==="final"){return super.set(_,X)}return undefined}}JustifyContent.names=["justify-content","flex-pack","box-pack"];JustifyContent.oldValues={"flex-end":"end","flex-start":"start","space-between":"justify","space-around":"distribute"};_.exports=JustifyContent},9115:(_,X,ee)=>{let te=ee(7251);class MaskBorder extends te{normalize(){return this.name.replace("box-image","border")}prefixed(_,X){let ee=super.prefixed(_,X);if(X==="-webkit-"){ee=ee.replace("border","box-image")}return ee}}MaskBorder.names=["mask-border","mask-border-source","mask-border-slice","mask-border-width","mask-border-outset","mask-border-repeat","mask-box-image","mask-box-image-source","mask-box-image-slice","mask-box-image-width","mask-box-image-outset","mask-box-image-repeat"];_.exports=MaskBorder},3338:(_,X,ee)=>{let te=ee(7251);class MaskComposite extends te{insert(_,X,ee){let te=_.prop==="mask-composite";let re;if(te){re=_.value.split(",")}else{re=_.value.match(MaskComposite.regexp)||[]}re=re.map((_=>_.trim())).filter((_=>_));let se=re.length;let ne;if(se){ne=this.clone(_);ne.value=re.map((_=>MaskComposite.oldValues[_]||_)).join(", ");if(re.includes("intersect")){ne.value+=", xor"}ne.prop=X+"mask-composite"}if(te){if(!se){return undefined}if(this.needCascade(_)){ne.raws.before=this.calcBefore(ee,_,X)}return _.parent.insertBefore(_,ne)}let ie=this.clone(_);ie.prop=X+ie.prop;if(se){ie.value=ie.value.replace(MaskComposite.regexp,"")}if(this.needCascade(_)){ie.raws.before=this.calcBefore(ee,_,X)}_.parent.insertBefore(_,ie);if(!se){return _}if(this.needCascade(_)){ne.raws.before=this.calcBefore(ee,_,X)}return _.parent.insertBefore(_,ne)}}MaskComposite.names=["mask","mask-composite"];MaskComposite.oldValues={add:"source-over",subtract:"source-out",intersect:"source-in",exclude:"xor"};MaskComposite.regexp=new RegExp(`\\s+(${Object.keys(MaskComposite.oldValues).join("|")})\\b(?!\\))\\s*(?=[,])`,"ig");_.exports=MaskComposite},5200:(_,X,ee)=>{let te=ee(7582);let re=ee(7251);class Order extends re{prefixed(_,X){let ee;[ee,X]=te(X);if(ee===2009){return X+"box-ordinal-group"}if(ee===2012){return X+"flex-order"}return super.prefixed(_,X)}normalize(){return"order"}set(_,X){let ee=te(X)[0];if(ee===2009&&/\d/.test(_.value)){_.value=(parseInt(_.value)+1).toString();return super.set(_,X)}return super.set(_,X)}}Order.names=["order","flex-order","box-ordinal-group"];_.exports=Order},674:(_,X,ee)=>{let te=ee(7251);class OverscrollBehavior extends te{prefixed(_,X){return X+"scroll-chaining"}normalize(){return"overscroll-behavior"}set(_,X){if(_.value==="auto"){_.value="chained"}else if(_.value==="none"||_.value==="contain"){_.value="none"}return super.set(_,X)}}OverscrollBehavior.names=["overscroll-behavior","scroll-chaining"];_.exports=OverscrollBehavior},2744:(_,X,ee)=>{let te=ee(3660);let re=ee(3712);class Pixelated extends re{replace(_,X){if(X==="-webkit-"){return _.replace(this.regexp(),"$1-webkit-optimize-contrast")}if(X==="-moz-"){return _.replace(this.regexp(),"$1-moz-crisp-edges")}return super.replace(_,X)}old(_){if(_==="-webkit-"){return new te(this.name,"-webkit-optimize-contrast")}if(_==="-moz-"){return new te(this.name,"-moz-crisp-edges")}return super.old(_)}}Pixelated.names=["pixelated"];_.exports=Pixelated},6001:(_,X,ee)=>{let te=ee(7251);let re=ee(2671);class PlaceSelf extends te{insert(_,X,ee){if(X!=="-ms-")return super.insert(_,X,ee);if(_.parent.some((_=>_.prop==="-ms-grid-row-align"))){return undefined}let[[te,se]]=re.parse(_);if(se){re.insertDecl(_,"grid-row-align",te);re.insertDecl(_,"grid-column-align",se)}else{re.insertDecl(_,"grid-row-align",te);re.insertDecl(_,"grid-column-align",te)}return undefined}}PlaceSelf.names=["place-self"];_.exports=PlaceSelf},8603:(_,X,ee)=>{let te=ee(3292);class PlaceholderShown extends te{prefixed(_){if(_==="-ms-"){return":-ms-input-placeholder"}return`:${_}placeholder-shown`}}PlaceholderShown.names=[":placeholder-shown"];_.exports=PlaceholderShown},1358:(_,X,ee)=>{let te=ee(3292);class Placeholder extends te{possible(){return super.possible().concat(["-moz- old","-ms- old"])}prefixed(_){if(_==="-webkit-"){return"::-webkit-input-placeholder"}if(_==="-ms-"){return"::-ms-input-placeholder"}if(_==="-ms- old"){return":-ms-input-placeholder"}if(_==="-moz- old"){return":-moz-placeholder"}return`::${_}placeholder`}}Placeholder.names=["::placeholder"];_.exports=Placeholder},6552:(_,X,ee)=>{let te=ee(7251);class TextDecorationSkipInk extends te{set(_,X){if(_.prop==="text-decoration-skip-ink"&&_.value==="auto"){_.prop=X+"text-decoration-skip";_.value="ink";return _}else{return super.set(_,X)}}}TextDecorationSkipInk.names=["text-decoration-skip-ink","text-decoration-skip"];_.exports=TextDecorationSkipInk},3499:(_,X,ee)=>{let te=ee(7251);const re=["none","underline","overline","line-through","blink","inherit","initial","unset"];class TextDecoration extends te{check(_){return _.value.split(/\s+/).some((_=>!re.includes(_)))}}TextDecoration.names=["text-decoration"];_.exports=TextDecoration},6389:(_,X,ee)=>{let te=ee(7251);class TextEmphasisPosition extends te{set(_,X){if(X==="-webkit-"){_.value=_.value.replace(/\s*(right|left)\s*/i,"")}return super.set(_,X)}}TextEmphasisPosition.names=["text-emphasis-position"];_.exports=TextEmphasisPosition},2287:(_,X,ee)=>{let te=ee(7251);class TransformDecl extends te{keyframeParents(_){let{parent:X}=_;while(X){if(X.type==="atrule"&&X.name==="keyframes"){return true}({parent:X}=X)}return false}contain3d(_){if(_.prop==="transform-origin"){return false}for(let X of TransformDecl.functions3d){if(_.value.includes(`${X}(`)){return true}}return false}set(_,X){_=super.set(_,X);if(X==="-ms-"){_.value=_.value.replace(/rotatez/gi,"rotate")}return _}insert(_,X,ee){if(X==="-ms-"){if(!this.contain3d(_)&&!this.keyframeParents(_)){return super.insert(_,X,ee)}}else if(X==="-o-"){if(!this.contain3d(_)){return super.insert(_,X,ee)}}else{return super.insert(_,X,ee)}return undefined}}TransformDecl.names=["transform","transform-origin"];TransformDecl.functions3d=["matrix3d","translate3d","translateZ","scale3d","scaleZ","rotate3d","rotateX","rotateY","perspective"];_.exports=TransformDecl},785:(_,X,ee)=>{let te=ee(7251);class UserSelect extends te{set(_,X){if(X==="-ms-"&&_.value==="contain"){_.value="element"}return super.set(_,X)}insert(_,X,ee){if(_.value==="all"&&X==="-ms-"){return undefined}else{return super.insert(_,X,ee)}}}UserSelect.names=["user-select"];_.exports=UserSelect},5746:(_,X,ee)=>{let te=ee(7251);class WritingMode extends te{insert(_,X,ee){if(X==="-ms-"){let te=this.set(this.clone(_),X);if(this.needCascade(_)){te.raws.before=this.calcBefore(ee,_,X)}let re="ltr";_.parent.nodes.forEach((_=>{if(_.prop==="direction"){if(_.value==="rtl"||_.value==="ltr")re=_.value}}));te.value=WritingMode.msValues[re][_.value]||_.value;return _.parent.insertBefore(_,te)}return super.insert(_,X,ee)}}WritingMode.names=["writing-mode"];WritingMode.msValues={ltr:{"horizontal-tb":"lr-tb","vertical-rl":"tb-rl","vertical-lr":"tb-lr"},rtl:{"horizontal-tb":"rl-tb","vertical-rl":"bt-rl","vertical-lr":"bt-lr"}};_.exports=WritingMode},1405:(_,X,ee)=>{let te=ee(4907);function capitalize(_){return _.slice(0,1).toUpperCase()+_.slice(1)}const re={ie:"IE",ie_mob:"IE Mobile",ios_saf:"iOS Safari",op_mini:"Opera Mini",op_mob:"Opera Mobile",and_chr:"Chrome for Android",and_ff:"Firefox for Android",and_uc:"UC for Android",and_qq:"QQ Browser",kaios:"KaiOS Browser",baidu:"Baidu Browser",samsung:"Samsung Internet"};function prefix(_,X,ee){let te=`  ${_}`;if(ee)te+=" *";te+=": ";te+=X.map((_=>_.replace(/^-(.*)-$/g,"$1"))).join(", ");te+="\n";return te}_.exports=function(_){if(_.browsers.selected.length===0){return"No browsers selected"}let X={};for(let ee of _.browsers.selected){let _=ee.split(" ");let te=_[0];let se=_[1];te=re[te]||capitalize(te);if(X[te]){X[te].push(se)}else{X[te]=[se]}}let ee="Browsers:\n";for(let _ in X){let te=X[_];te=te.sort(((_,X)=>parseFloat(X)-parseFloat(_)));ee+=`  ${_}: ${te.join(", ")}\n`}let se=te.coverage(_.browsers.selected);let ne=Math.round(se*100)/100;ee+=`\nThese browsers account for ${ne}% of all users globally\n`;let ie=[];for(let X in _.add){let ee=_.add[X];if(X[0]==="@"&&ee.prefixes){ie.push(prefix(X,ee.prefixes))}}if(ie.length>0){ee+=`\nAt-Rules:\n${ie.sort().join("")}`}let oe=[];for(let X of _.add.selectors){if(X.prefixes){oe.push(prefix(X.name,X.prefixes))}}if(oe.length>0){ee+=`\nSelectors:\n${oe.sort().join("")}`}let ae=[];let le=[];let ue=false;for(let X in _.add){let ee=_.add[X];if(X[0]!=="@"&&ee.prefixes){let _=X.indexOf("grid-")===0;if(_)ue=true;le.push(prefix(X,ee.prefixes,_))}if(!Array.isArray(ee.values)){continue}for(let _ of ee.values){let X=_.name.includes("grid");if(X)ue=true;let ee=prefix(_.name,_.prefixes,X);if(!ae.includes(ee)){ae.push(ee)}}}if(le.length>0){ee+=`\nProperties:\n${le.sort().join("")}`}if(ae.length>0){ee+=`\nValues:\n${ae.sort().join("")}`}if(ue){ee+="\n* - Prefixes will be added only on grid: true option.\n"}if(!ie.length&&!oe.length&&!le.length&&!ae.length){ee+="\nAwesome! Your browsers don't require any vendor prefixes."+"\nNow you can remove Autoprefixer from build steps."}return ee}},9936:_=>{class OldSelector{constructor(_,X){this.prefix=X;this.prefixed=_.prefixed(this.prefix);this.regexp=_.regexp(this.prefix);this.prefixeds=_.possible().map((X=>[_.prefixed(X),_.regexp(X)]));this.unprefixed=_.name;this.nameRegexp=_.regexp()}isHack(_){let X=_.parent.index(_)+1;let ee=_.parent.nodes;while(X<ee.length){let _=ee[X].selector;if(!_){return true}if(_.includes(this.unprefixed)&&_.match(this.nameRegexp)){return false}let te=false;for(let[X,ee]of this.prefixeds){if(_.includes(X)&&_.match(ee)){te=true;break}}if(!te){return true}X+=1}return true}check(_){if(!_.selector.includes(this.prefixed)){return false}if(!_.selector.match(this.regexp)){return false}if(this.isHack(_)){return false}return true}}_.exports=OldSelector},3660:(_,X,ee)=>{let te=ee(4012);class OldValue{constructor(_,X,ee,re){this.unprefixed=_;this.prefixed=X;this.string=ee||X;this.regexp=re||te.regexp(X)}check(_){if(_.includes(this.string)){return!!_.match(this.regexp)}return false}}_.exports=OldValue},4877:(_,X,ee)=>{let te=ee(1901);let re=ee(5965);let se=ee(4012);function clone(_,X){let ee=new _.constructor;for(let te of Object.keys(_||{})){let re=_[te];if(te==="parent"&&typeof re==="object"){if(X){ee[te]=X}}else if(te==="source"||te===null){ee[te]=re}else if(Array.isArray(re)){ee[te]=re.map((_=>clone(_,ee)))}else if(te!=="_autoprefixerPrefix"&&te!=="_autoprefixerValues"&&te!=="proxyCache"){if(typeof re==="object"&&re!==null){re=clone(re,ee)}ee[te]=re}}return ee}class Prefixer{static hack(_){if(!this.hacks){this.hacks={}}return _.names.map((X=>{this.hacks[X]=_;return this.hacks[X]}))}static load(_,X,ee){let te=this.hacks&&this.hacks[_];if(te){return new te(_,X,ee)}else{return new this(_,X,ee)}}static clone(_,X){let ee=clone(_);for(let _ in X){ee[_]=X[_]}return ee}constructor(_,X,ee){this.prefixes=X;this.name=_;this.all=ee}parentPrefix(_){let X;if(typeof _._autoprefixerPrefix!=="undefined"){X=_._autoprefixerPrefix}else if(_.type==="decl"&&_.prop[0]==="-"){X=re.prefix(_.prop)}else if(_.type==="root"){X=false}else if(_.type==="rule"&&_.selector.includes(":-")&&/:(-\w+-)/.test(_.selector)){X=_.selector.match(/:(-\w+-)/)[1]}else if(_.type==="atrule"&&_.name[0]==="-"){X=re.prefix(_.name)}else{X=this.parentPrefix(_.parent)}if(!te.prefixes().includes(X)){X=false}_._autoprefixerPrefix=X;return _._autoprefixerPrefix}process(_,X){if(!this.check(_)){return undefined}let ee=this.parentPrefix(_);let te=this.prefixes.filter((_=>!ee||ee===se.removeNote(_)));let re=[];for(let ee of te){if(this.add(_,ee,re.concat([ee]),X)){re.push(ee)}}return re}clone(_,X){return Prefixer.clone(_,X)}}_.exports=Prefixer},7223:(_,X,ee)=>{let te=ee(5965);let re=ee(7251);let se=ee(9515);let ne=ee(8346);let ie=ee(8243);let oe=ee(5362);let ae=ee(1901);let le=ee(3292);let ue=ee(3183);let ce=ee(3712);let pe=ee(4012);let fe=ee(8545);let de=ee(1358);let he=ee(8603);let me=ee(5277);let ge=ee(9332);let be=ee(5200);let ve=ee(751);let ye=ee(866);let we=ee(2753);let xe=ee(4288);let ke=ee(553);let Se=ee(7252);let _e=ee(4163);let Pe=ee(6001);let Oe=ee(8707);let je=ee(4741);let Te=ee(9191);let Ee=ee(3431);let Fe=ee(9115);let $e=ee(3338);let Me=ee(5199);let Re=ee(785);let Ae=ee(4274);let qe=ee(5128);let ze=ee(1741);let Ge=ee(5746);let Ue=ee(8423);let He=ee(5832);let Ze=ee(9800);let Ke=ee(3428);let Xe=ee(1006);let et=ee(4408);let tt=ee(8605);let rt=ee(2287);let st=ee(1445);let nt=ee(7084);let it=ee(1898);let ot=ee(758);let lt=ee(3499);let ut=ee(2009);let ct=ee(3177);let pt=ee(4917);let ft=ee(5826);let dt=ee(7027);let ht=ee(674);let mt=ee(8881);let gt=ee(6389);let bt=ee(6552);let vt=ee(7222);let yt=ee(9177);let wt=ee(2744);let xt=ee(7541);let kt=ee(3914);let St=ee(2082);let _t=ee(5067);let Pt=ee(2777);let Ot=ee(6564);le.hack(Ot);le.hack(fe);le.hack(de);le.hack(he);le.hack(me);re.hack(ge);re.hack(be);re.hack(ve);re.hack(ye);re.hack(we);re.hack(xe);re.hack(ke);re.hack(Se);re.hack(_e);re.hack(Pe);re.hack(Oe);re.hack(je);re.hack(Te);re.hack(Ee);re.hack(Fe);re.hack($e);re.hack(Me);re.hack(Re);re.hack(Ae);re.hack(qe);re.hack(ze);re.hack(Ge);re.hack(Ue);re.hack(He);re.hack(Ze);re.hack(Ke);re.hack(Xe);re.hack(et);re.hack(tt);re.hack(rt);re.hack(st);re.hack(nt);re.hack(it);re.hack(ot);re.hack(lt);re.hack(ut);re.hack(ct);re.hack(pt);re.hack(ft);re.hack(dt);re.hack(ht);re.hack(mt);re.hack(gt);re.hack(bt);ce.hack(vt);ce.hack(yt);ce.hack(wt);ce.hack(xt);ce.hack(kt);ce.hack(St);ce.hack(_t);ce.hack(Pt);let Ct=new Map;class Prefixes{constructor(_,X,ee={}){this.data=_;this.browsers=X;this.options=ee;[this.add,this.remove]=this.preprocess(this.select(this.data));this.transition=new ne(this);this.processor=new ie(this)}cleaner(){if(this.cleanerCache){return this.cleanerCache}if(this.browsers.selected.length){let _=new ae(this.browsers.data,[]);this.cleanerCache=new Prefixes(this.data,_,this.options)}else{return this}return this.cleanerCache}select(_){let X={add:{},remove:{}};for(let ee in _){let te=_[ee];let re=te.browsers.map((_=>{let X=_.split(" ");return{browser:`${X[0]} ${X[1]}`,note:X[2]}}));let se=re.filter((_=>_.note)).map((_=>`${this.browsers.prefix(_.browser)} ${_.note}`));se=pe.uniq(se);re=re.filter((_=>this.browsers.isSelected(_.browser))).map((_=>{let X=this.browsers.prefix(_.browser);if(_.note){return`${X} ${_.note}`}else{return X}}));re=this.sort(pe.uniq(re));if(this.options.flexbox==="no-2009"){re=re.filter((_=>!_.includes("2009")))}let ne=te.browsers.map((_=>this.browsers.prefix(_)));if(te.mistakes){ne=ne.concat(te.mistakes)}ne=ne.concat(se);ne=pe.uniq(ne);if(re.length){X.add[ee]=re;if(re.length<ne.length){X.remove[ee]=ne.filter((_=>!re.includes(_)))}}else{X.remove[ee]=ne}}return X}sort(_){return _.sort(((_,X)=>{let ee=pe.removeNote(_).length;let te=pe.removeNote(X).length;if(ee===te){return X.length-_.length}else{return te-ee}}))}preprocess(_){let X={selectors:[],"@supports":new oe(Prefixes,this)};for(let ee in _.add){let te=_.add[ee];if(ee==="@keyframes"||ee==="@viewport"){X[ee]=new ue(ee,te,this)}else if(ee==="@resolution"){X[ee]=new se(ee,te,this)}else if(this.data[ee].selector){X.selectors.push(le.load(ee,te,this))}else{let _=this.data[ee].props;if(_){let re=ce.load(ee,te,this);for(let ee of _){if(!X[ee]){X[ee]={values:[]}}X[ee].values.push(re)}}else{let _=X[ee]&&X[ee].values||[];X[ee]=re.load(ee,te,this);X[ee].values=_}}}let ee={selectors:[]};for(let te in _.remove){let re=_.remove[te];if(this.data[te].selector){let _=le.load(te,re);for(let X of re){ee.selectors.push(_.old(X))}}else if(te==="@keyframes"||te==="@viewport"){for(let _ of re){let X=`@${_}${te.slice(1)}`;ee[X]={remove:true}}}else if(te==="@resolution"){ee[te]=new se(te,re,this)}else{let _=this.data[te].props;if(_){let X=ce.load(te,[],this);for(let te of re){let re=X.old(te);if(re){for(let X of _){if(!ee[X]){ee[X]={}}if(!ee[X].values){ee[X].values=[]}ee[X].values.push(re)}}}}else{for(let _ of re){let re=this.decl(te).old(te,_);if(te==="align-self"){let ee=X[te]&&X[te].prefixes;if(ee){if(_==="-webkit- 2009"&&ee.includes("-webkit-")){continue}else if(_==="-webkit-"&&ee.includes("-webkit- 2009")){continue}}}for(let _ of re){if(!ee[_]){ee[_]={}}ee[_].remove=true}}}}}return[X,ee]}decl(_){if(!Ct.has(_)){Ct.set(_,re.load(_))}return Ct.get(_)}unprefixed(_){let X=this.normalize(te.unprefixed(_));if(X==="flex-direction"){X="flex-flow"}return X}normalize(_){return this.decl(_).normalize(_)}prefixed(_,X){_=te.unprefixed(_);return this.decl(_).prefixed(_,X)}values(_,X){let ee=this[_];let te=ee["*"]&&ee["*"].values;let re=ee[X]&&ee[X].values;if(te&&re){return pe.uniq(te.concat(re))}else{return te||re||[]}}group(_){let X=_.parent;let ee=X.index(_);let{length:te}=X.nodes;let re=this.unprefixed(_.prop);let checker=(_,se)=>{ee+=_;while(ee>=0&&ee<te){let te=X.nodes[ee];if(te.type==="decl"){if(_===-1&&te.prop===re){if(!ae.withPrefix(te.value)){break}}if(this.unprefixed(te.prop)!==re){break}else if(se(te)===true){return true}if(_===+1&&te.prop===re){if(!ae.withPrefix(te.value)){break}}}ee+=_}return false};return{up(_){return checker(-1,_)},down(_){return checker(+1,_)}}}}_.exports=Prefixes},8243:(_,X,ee)=>{let te=ee(2045);let re=ee(3712);let se=ee(2671).insertAreas;const ne=/(^|[^-])linear-gradient\(\s*(top|left|right|bottom)/i;const ie=/(^|[^-])radial-gradient\(\s*\d+(\w*|%)\s+\d+(\w*|%)\s*,/i;const oe=/(!\s*)?autoprefixer:\s*ignore\s+next/i;const ae=/(!\s*)?autoprefixer\s*grid:\s*(on|off|(no-)?autoplace)/i;const le=["width","height","min-width","max-width","min-height","max-height","inline-size","min-inline-size","max-inline-size","block-size","min-block-size","max-block-size"];function hasGridTemplate(_){return _.parent.some((_=>_.prop==="grid-template"||_.prop==="grid-template-areas"))}function hasRowsAndColumns(_){let X=_.parent.some((_=>_.prop==="grid-template-rows"));let ee=_.parent.some((_=>_.prop==="grid-template-columns"));return X&&ee}class Processor{constructor(_){this.prefixes=_}add(_,X){let ee=this.prefixes.add["@resolution"];let oe=this.prefixes.add["@keyframes"];let ae=this.prefixes.add["@viewport"];let ue=this.prefixes.add["@supports"];_.walkAtRules((_=>{if(_.name==="keyframes"){if(!this.disabled(_,X)){return oe&&oe.process(_)}}else if(_.name==="viewport"){if(!this.disabled(_,X)){return ae&&ae.process(_)}}else if(_.name==="supports"){if(this.prefixes.options.supports!==false&&!this.disabled(_,X)){return ue.process(_)}}else if(_.name==="media"&&_.params.includes("-resolution")){if(!this.disabled(_,X)){return ee&&ee.process(_)}}return undefined}));_.walkRules((_=>{if(this.disabled(_,X))return undefined;return this.prefixes.add.selectors.map((ee=>ee.process(_,X)))}));function insideGrid(_){return _.parent.nodes.some((_=>{if(_.type!=="decl")return false;let X=_.prop==="display"&&/(inline-)?grid/.test(_.value);let ee=_.prop.startsWith("grid-template");let te=/^grid-([A-z]+-)?gap/.test(_.prop);return X||ee||te}))}function insideFlex(_){return _.parent.some((_=>_.prop==="display"&&/(inline-)?flex/.test(_.value)))}let ce=this.gridStatus(_,X)&&this.prefixes.add["grid-area"]&&this.prefixes.add["grid-area"].prefixes;_.walkDecls((_=>{if(this.disabledDecl(_,X))return undefined;let ee=_.parent;let re=_.prop;let se=_.value;if(re==="grid-row-span"){X.warn("grid-row-span is not part of final Grid Layout. Use grid-row.",{node:_});return undefined}else if(re==="grid-column-span"){X.warn("grid-column-span is not part of final Grid Layout. Use grid-column.",{node:_});return undefined}else if(re==="display"&&se==="box"){X.warn("You should write display: flex by final spec "+"instead of display: box",{node:_});return undefined}else if(re==="text-emphasis-position"){if(se==="under"||se==="over"){X.warn("You should use 2 values for text-emphasis-position "+"For example, `under left` instead of just `under`.",{node:_})}}else if(/^(align|justify|place)-(items|content)$/.test(re)&&insideFlex(_)){if(se==="start"||se==="end"){X.warn(`${se} value has mixed support, consider using `+`flex-${se} instead`,{node:_})}}else if(re==="text-decoration-skip"&&se==="ink"){X.warn("Replace text-decoration-skip: ink to "+"text-decoration-skip-ink: auto, because spec had been changed",{node:_})}else{if(ce&&this.gridStatus(_,X)){if(_.value==="subgrid"){X.warn("IE does not support subgrid",{node:_})}if(/^(align|justify|place)-items$/.test(re)&&insideGrid(_)){let ee=re.replace("-items","-self");X.warn(`IE does not support ${re} on grid containers. `+`Try using ${ee} on child elements instead: `+`${_.parent.selector} > * { ${ee}: ${_.value} }`,{node:_})}else if(/^(align|justify|place)-content$/.test(re)&&insideGrid(_)){X.warn(`IE does not support ${_.prop} on grid containers`,{node:_})}else if(re==="display"&&_.value==="contents"){X.warn("Please do not use display: contents; "+"if you have grid setting enabled",{node:_});return undefined}else if(_.prop==="grid-gap"){let ee=this.gridStatus(_,X);if(ee==="autoplace"&&!hasRowsAndColumns(_)&&!hasGridTemplate(_)){X.warn("grid-gap only works if grid-template(-areas) is being "+"used or both rows and columns have been declared "+"and cells have not been manually "+"placed inside the explicit grid",{node:_})}else if((ee===true||ee==="no-autoplace")&&!hasGridTemplate(_)){X.warn("grid-gap only works if grid-template(-areas) is being used",{node:_})}}else if(re==="grid-auto-columns"){X.warn("grid-auto-columns is not supported by IE",{node:_});return undefined}else if(re==="grid-auto-rows"){X.warn("grid-auto-rows is not supported by IE",{node:_});return undefined}else if(re==="grid-auto-flow"){let te=ee.some((_=>_.prop==="grid-template-rows"));let re=ee.some((_=>_.prop==="grid-template-columns"));if(hasGridTemplate(_)){X.warn("grid-auto-flow is not supported by IE",{node:_})}else if(se.includes("dense")){X.warn("grid-auto-flow: dense is not supported by IE",{node:_})}else if(!te&&!re){X.warn("grid-auto-flow works only if grid-template-rows and "+"grid-template-columns are present in the same rule",{node:_})}return undefined}else if(se.includes("auto-fit")){X.warn("auto-fit value is not supported by IE",{node:_,word:"auto-fit"});return undefined}else if(se.includes("auto-fill")){X.warn("auto-fill value is not supported by IE",{node:_,word:"auto-fill"});return undefined}else if(re.startsWith("grid-template")&&se.includes("[")){X.warn("Autoprefixer currently does not support line names. "+"Try using grid-template-areas instead.",{node:_,word:"["})}}if(se.includes("radial-gradient")){if(ie.test(_.value)){X.warn("Gradient has outdated direction syntax. "+"New syntax is like `closest-side at 0 0` "+"instead of `0 0, closest-side`.",{node:_})}else{let ee=te(se);for(let te of ee.nodes){if(te.type==="function"&&te.value==="radial-gradient"){for(let ee of te.nodes){if(ee.type==="word"){if(ee.value==="cover"){X.warn("Gradient has outdated direction syntax. "+"Replace `cover` to `farthest-corner`.",{node:_})}else if(ee.value==="contain"){X.warn("Gradient has outdated direction syntax. "+"Replace `contain` to `closest-side`.",{node:_})}}}}}}}if(se.includes("linear-gradient")){if(ne.test(se)){X.warn("Gradient has outdated direction syntax. "+"New syntax is like `to left` instead of `right`.",{node:_})}}}if(le.includes(_.prop)){if(!_.value.includes("-fill-available")){if(_.value.includes("fill-available")){X.warn("Replace fill-available to stretch, "+"because spec had been changed",{node:_})}else if(_.value.includes("fill")){let ee=te(se);if(ee.nodes.some((_=>_.type==="word"&&_.value==="fill"))){X.warn("Replace fill to stretch, because spec had been changed",{node:_})}}}}let oe;if(_.prop==="transition"||_.prop==="transition-property"){return this.prefixes.transition.add(_,X)}else if(_.prop==="align-self"){let ee=this.displayType(_);if(ee!=="grid"&&this.prefixes.options.flexbox!==false){oe=this.prefixes.add["align-self"];if(oe&&oe.prefixes){oe.process(_)}}if(this.gridStatus(_,X)!==false){oe=this.prefixes.add["grid-row-align"];if(oe&&oe.prefixes){return oe.process(_,X)}}}else if(_.prop==="justify-self"){if(this.gridStatus(_,X)!==false){oe=this.prefixes.add["grid-column-align"];if(oe&&oe.prefixes){return oe.process(_,X)}}}else if(_.prop==="place-self"){oe=this.prefixes.add["place-self"];if(oe&&oe.prefixes&&this.gridStatus(_,X)!==false){return oe.process(_,X)}}else{oe=this.prefixes.add[_.prop];if(oe&&oe.prefixes){return oe.process(_,X)}}return undefined}));if(this.gridStatus(_,X)){se(_,this.disabled)}return _.walkDecls((_=>{if(this.disabledValue(_,X))return;let ee=this.prefixes.unprefixed(_.prop);let te=this.prefixes.values("add",ee);if(Array.isArray(te)){for(let ee of te){if(ee.process)ee.process(_,X)}}re.save(this.prefixes,_)}))}remove(_,X){let ee=this.prefixes.remove["@resolution"];_.walkAtRules(((_,te)=>{if(this.prefixes.remove[`@${_.name}`]){if(!this.disabled(_,X)){_.parent.removeChild(te)}}else if(_.name==="media"&&_.params.includes("-resolution")&&ee){ee.clean(_)}}));for(let ee of this.prefixes.remove.selectors){_.walkRules(((_,te)=>{if(ee.check(_)){if(!this.disabled(_,X)){_.parent.removeChild(te)}}}))}return _.walkDecls(((_,ee)=>{if(this.disabled(_,X))return;let te=_.parent;let re=this.prefixes.unprefixed(_.prop);if(_.prop==="transition"||_.prop==="transition-property"){this.prefixes.transition.remove(_)}if(this.prefixes.remove[_.prop]&&this.prefixes.remove[_.prop].remove){let X=this.prefixes.group(_).down((_=>this.prefixes.normalize(_.prop)===re));if(re==="flex-flow"){X=true}if(_.prop==="-webkit-box-orient"){let X={"flex-direction":true,"flex-flow":true};if(!_.parent.some((_=>X[_.prop])))return}if(X&&!this.withHackValue(_)){if(_.raw("before").includes("\n")){this.reduceSpaces(_)}te.removeChild(ee);return}}for(let X of this.prefixes.values("remove",re)){if(!X.check)continue;if(!X.check(_.value))continue;re=X.unprefixed;let se=this.prefixes.group(_).down((_=>_.value.includes(re)));if(se){te.removeChild(ee);return}}}))}withHackValue(_){return _.prop==="-webkit-background-clip"&&_.value==="text"}disabledValue(_,X){if(this.gridStatus(_,X)===false&&_.type==="decl"){if(_.prop==="display"&&_.value.includes("grid")){return true}}if(this.prefixes.options.flexbox===false&&_.type==="decl"){if(_.prop==="display"&&_.value.includes("flex")){return true}}if(_.type==="decl"&&_.prop==="content"){return true}return this.disabled(_,X)}disabledDecl(_,X){if(this.gridStatus(_,X)===false&&_.type==="decl"){if(_.prop.includes("grid")||_.prop==="justify-items"){return true}}if(this.prefixes.options.flexbox===false&&_.type==="decl"){let X=["order","justify-content","align-items","align-content"];if(_.prop.includes("flex")||X.includes(_.prop)){return true}}return this.disabled(_,X)}disabled(_,X){if(!_)return false;if(_._autoprefixerDisabled!==undefined){return _._autoprefixerDisabled}if(_.parent){let X=_.prev();if(X&&X.type==="comment"&&oe.test(X.text)){_._autoprefixerDisabled=true;_._autoprefixerSelfDisabled=true;return true}}let ee=null;if(_.nodes){let te;_.each((_=>{if(_.type!=="comment")return;if(/(!\s*)?autoprefixer:\s*(off|on)/i.test(_.text)){if(typeof te!=="undefined"){X.warn("Second Autoprefixer control comment "+"was ignored. Autoprefixer applies control "+"comment to whole block, not to next rules.",{node:_})}else{te=/on/i.test(_.text)}}}));if(te!==undefined){ee=!te}}if(!_.nodes||ee===null){if(_.parent){let te=this.disabled(_.parent,X);if(_.parent._autoprefixerSelfDisabled===true){ee=false}else{ee=te}}else{ee=false}}_._autoprefixerDisabled=ee;return ee}reduceSpaces(_){let X=false;this.prefixes.group(_).up((()=>{X=true;return true}));if(X){return}let ee=_.raw("before").split("\n");let te=ee[ee.length-1].length;let re=false;this.prefixes.group(_).down((_=>{ee=_.raw("before").split("\n");let X=ee.length-1;if(ee[X].length>te){if(re===false){re=ee[X].length-te}ee[X]=ee[X].slice(0,-re);_.raws.before=ee.join("\n")}}))}displayType(_){for(let X of _.parent.nodes){if(X.prop!=="display"){continue}if(X.value.includes("flex")){return"flex"}if(X.value.includes("grid")){return"grid"}}return false}gridStatus(_,X){if(!_)return false;if(_._autoprefixerGridStatus!==undefined){return _._autoprefixerGridStatus}let ee=null;if(_.nodes){let te;_.each((_=>{if(_.type!=="comment")return;if(ae.test(_.text)){let ee=/:\s*autoplace/i.test(_.text);let re=/no-autoplace/i.test(_.text);if(typeof te!=="undefined"){X.warn("Second Autoprefixer grid control comment was "+"ignored. Autoprefixer applies control comments to the whole "+"block, not to the next rules.",{node:_})}else if(ee){te="autoplace"}else if(re){te=true}else{te=/on/i.test(_.text)}}}));if(te!==undefined){ee=te}}if(_.type==="atrule"&&_.name==="supports"){let X=_.params;if(X.includes("grid")&&X.includes("auto")){ee=false}}if(!_.nodes||ee===null){if(_.parent){let te=this.gridStatus(_.parent,X);if(_.parent._autoprefixerSelfDisabled===true){ee=false}else{ee=te}}else if(typeof this.prefixes.options.grid!=="undefined"){ee=this.prefixes.options.grid}else if(typeof process.env.AUTOPREFIXER_GRID!=="undefined"){if(process.env.AUTOPREFIXER_GRID==="autoplace"){ee="autoplace"}else{ee=true}}else{ee=false}}_._autoprefixerGridStatus=ee;return ee}}_.exports=Processor},9515:(_,X,ee)=>{let te=ee(3227);let re=ee(4877);let se=ee(4012);const ne=/(min|max)-resolution\s*:\s*\d*\.?\d+(dppx|dpcm|dpi|x)/gi;const ie=/(min|max)-resolution(\s*:\s*)(\d*\.?\d+)(dppx|dpcm|dpi|x)/i;class Resolution extends re{prefixName(_,X){if(_==="-moz-"){return X+"--moz-device-pixel-ratio"}else{return _+X+"-device-pixel-ratio"}}prefixQuery(_,X,ee,re,se){re=new te(re);if(se==="dpi"){re=re.div(96)}else if(se==="dpcm"){re=re.mul(2.54).div(96)}re=re.simplify();if(_==="-o-"){re=re.n+"/"+re.d}return this.prefixName(_,X)+ee+re}clean(_){if(!this.bad){this.bad=[];for(let _ of this.prefixes){this.bad.push(this.prefixName(_,"min"));this.bad.push(this.prefixName(_,"max"))}}_.params=se.editList(_.params,(_=>_.filter((_=>this.bad.every((X=>!_.includes(X)))))))}process(_){let X=this.parentPrefix(_);let ee=X?[X]:this.prefixes;_.params=se.editList(_.params,((_,X)=>{for(let te of _){if(!te.includes("min-resolution")&&!te.includes("max-resolution")){X.push(te);continue}for(let _ of ee){let ee=te.replace(ne,(X=>{let ee=X.match(ie);return this.prefixQuery(_,ee[1],ee[2],ee[3],ee[4])}));X.push(ee)}X.push(te)}return se.uniq(X)}))}}_.exports=Resolution},3292:(_,X,ee)=>{let{list:te}=ee(977);let re=ee(9936);let se=ee(4877);let ne=ee(1901);let ie=ee(4012);class Selector extends se{constructor(_,X,ee){super(_,X,ee);this.regexpCache=new Map}check(_){if(_.selector.includes(this.name)){return!!_.selector.match(this.regexp())}return false}prefixed(_){return this.name.replace(/^(\W*)/,`$1${_}`)}regexp(_){if(!this.regexpCache.has(_)){let X=_?this.prefixed(_):this.name;this.regexpCache.set(_,new RegExp(`(^|[^:"'=])${ie.escapeRegexp(X)}`,"gi"))}return this.regexpCache.get(_)}possible(){return ne.prefixes()}prefixeds(_){if(_._autoprefixerPrefixeds){if(_._autoprefixerPrefixeds[this.name]){return _._autoprefixerPrefixeds}}else{_._autoprefixerPrefixeds={}}let X={};if(_.selector.includes(",")){let ee=te.comma(_.selector);let re=ee.filter((_=>_.includes(this.name)));for(let _ of this.possible()){X[_]=re.map((X=>this.replace(X,_))).join(", ")}}else{for(let ee of this.possible()){X[ee]=this.replace(_.selector,ee)}}_._autoprefixerPrefixeds[this.name]=X;return _._autoprefixerPrefixeds}already(_,X,ee){let te=_.parent.index(_)-1;while(te>=0){let re=_.parent.nodes[te];if(re.type!=="rule"){return false}let se=false;for(let _ in X[this.name]){let te=X[this.name][_];if(re.selector===te){if(ee===_){return true}else{se=true;break}}}if(!se){return false}te-=1}return false}replace(_,X){return _.replace(this.regexp(),`$1${this.prefixed(X)}`)}add(_,X){let ee=this.prefixeds(_);if(this.already(_,ee,X)){return}let te=this.clone(_,{selector:ee[this.name][X]});_.parent.insertBefore(_,te)}old(_){return new re(this,_)}}_.exports=Selector},5362:(_,X,ee)=>{let te=ee(8944);let{feature:re}=ee(9613);let{parse:se}=ee(977);let ne=ee(1901);let ie=ee(3725);let oe=ee(3712);let ae=ee(4012);let le=re(te);let ue=[];for(let _ in le.stats){let X=le.stats[_];for(let ee in X){let te=X[ee];if(/y/.test(te)){ue.push(_+" "+ee)}}}class Supports{constructor(_,X){this.Prefixes=_;this.all=X}prefixer(){if(this.prefixerCache){return this.prefixerCache}let _=this.all.browsers.selected.filter((_=>ue.includes(_)));let X=new ne(this.all.browsers.data,_,this.all.options);this.prefixerCache=new this.Prefixes(this.all.data,X,this.all.options);return this.prefixerCache}parse(_){let X=_.split(":");let ee=X[0];let te=X[1];if(!te)te="";return[ee.trim(),te.trim()]}virtual(_){let[X,ee]=this.parse(_);let te=se("a{}").first;te.append({prop:X,value:ee,raws:{before:""}});return te}prefixed(_){let X=this.virtual(_);if(this.disabled(X.first)){return X.nodes}let ee={warn:()=>null};let te=this.prefixer().add[X.first.prop];te&&te.process&&te.process(X.first,ee);for(let _ of X.nodes){for(let ee of this.prefixer().values("add",X.first.prop)){ee.process(_)}oe.save(this.all,_)}return X.nodes}isNot(_){return typeof _==="string"&&/not\s*/i.test(_)}isOr(_){return typeof _==="string"&&/\s*or\s*/i.test(_)}isProp(_){return typeof _==="object"&&_.length===1&&typeof _[0]==="string"}isHack(_,X){let ee=new RegExp(`(\\(|\\s)${ae.escapeRegexp(X)}:`);return!ee.test(_)}toRemove(_,X){let[ee,te]=this.parse(_);let re=this.all.unprefixed(ee);let se=this.all.cleaner();if(se.remove[ee]&&se.remove[ee].remove&&!this.isHack(X,re)){return true}for(let _ of se.values("remove",re)){if(_.check(te)){return true}}return false}remove(_,X){let ee=0;while(ee<_.length){if(!this.isNot(_[ee-1])&&this.isProp(_[ee])&&this.isOr(_[ee+1])){if(this.toRemove(_[ee][0],X)){_.splice(ee,2);continue}ee+=2;continue}if(typeof _[ee]==="object"){_[ee]=this.remove(_[ee],X)}ee+=1}return _}cleanBrackets(_){return _.map((_=>{if(typeof _!=="object"){return _}if(_.length===1&&typeof _[0]==="object"){return this.cleanBrackets(_[0])}return this.cleanBrackets(_)}))}convert(_){let X=[""];for(let ee of _){X.push([`${ee.prop}: ${ee.value}`]);X.push(" or ")}X[X.length-1]="";return X}normalize(_){if(typeof _!=="object"){return _}_=_.filter((_=>_!==""));if(typeof _[0]==="string"){let X=_[0].trim();if(X.includes(":")||X==="selector"||X==="not selector"){return[ie.stringify(_)]}}return _.map((_=>this.normalize(_)))}add(_,X){return _.map((_=>{if(this.isProp(_)){let X=this.prefixed(_[0]);if(X.length>1){return this.convert(X)}return _}if(typeof _==="object"){return this.add(_,X)}return _}))}process(_){let X=ie.parse(_.params);X=this.normalize(X);X=this.remove(X,_.params);X=this.add(X,_.params);X=this.cleanBrackets(X);_.params=ie.stringify(X)}disabled(_){if(!this.all.options.grid){if(_.prop==="display"&&_.value.includes("grid")){return true}if(_.prop.includes("grid")||_.prop==="justify-items"){return true}}if(this.all.options.flexbox===false){if(_.prop==="display"&&_.value.includes("flex")){return true}let X=["order","justify-content","align-items","align-content"];if(_.prop.includes("flex")||X.includes(_.prop)){return true}}return false}}_.exports=Supports},8346:(_,X,ee)=>{let{list:te}=ee(977);let re=ee(2045);let se=ee(1901);let ne=ee(5965);class Transition{constructor(_){this.props=["transition","transition-property"];this.prefixes=_}add(_,X){let ee,te;let re=this.prefixes.add[_.prop];let se=this.ruleVendorPrefixes(_);let ne=se||re&&re.prefixes||[];let ie=this.parse(_.value);let oe=ie.map((_=>this.findProp(_)));let ae=[];if(oe.some((_=>_[0]==="-"))){return}for(let _ of ie){te=this.findProp(_);if(te[0]==="-")continue;let X=this.prefixes.add[te];if(!X||!X.prefixes)continue;for(ee of X.prefixes){if(se&&!se.some((_=>ee.includes(_)))){continue}let X=this.prefixes.prefixed(te,ee);if(X!=="-ms-transform"&&!oe.includes(X)){if(!this.disabled(te,ee)){ae.push(this.clone(te,X,_))}}}}ie=ie.concat(ae);let le=this.stringify(ie);let ue=this.stringify(this.cleanFromUnprefixed(ie,"-webkit-"));if(ne.includes("-webkit-")){this.cloneBefore(_,`-webkit-${_.prop}`,ue)}this.cloneBefore(_,_.prop,ue);if(ne.includes("-o-")){let X=this.stringify(this.cleanFromUnprefixed(ie,"-o-"));this.cloneBefore(_,`-o-${_.prop}`,X)}for(ee of ne){if(ee!=="-webkit-"&&ee!=="-o-"){let X=this.stringify(this.cleanOtherPrefixes(ie,ee));this.cloneBefore(_,ee+_.prop,X)}}if(le!==_.value&&!this.already(_,_.prop,le)){this.checkForWarning(X,_);_.cloneBefore();_.value=le}}findProp(_){let X=_[0].value;if(/^\d/.test(X)){for(let[X,ee]of _.entries()){if(X!==0&&ee.type==="word"){return ee.value}}}return X}already(_,X,ee){return _.parent.some((_=>_.prop===X&&_.value===ee))}cloneBefore(_,X,ee){if(!this.already(_,X,ee)){_.cloneBefore({prop:X,value:ee})}}checkForWarning(_,X){if(X.prop!=="transition-property"){return}let ee=false;let re=false;X.parent.each((_=>{if(_.type!=="decl"){return undefined}if(_.prop.indexOf("transition-")!==0){return undefined}let X=te.comma(_.value);if(_.prop==="transition-property"){X.forEach((_=>{let X=this.prefixes.add[_];if(X&&X.prefixes&&X.prefixes.length>0){ee=true}}));return undefined}re=re||X.length>1;return false}));if(ee&&re){X.warn(_,"Replace transition-property to transition, "+"because Autoprefixer could not support "+"any cases of transition-property "+"and other transition-*")}}remove(_){let X=this.parse(_.value);X=X.filter((_=>{let X=this.prefixes.remove[this.findProp(_)];return!X||!X.remove}));let ee=this.stringify(X);if(_.value===ee){return}if(X.length===0){_.remove();return}let te=_.parent.some((X=>X.prop===_.prop&&X.value===ee));let re=_.parent.some((X=>X!==_&&X.prop===_.prop&&X.value.length>ee.length));if(te||re){_.remove();return}_.value=ee}parse(_){let X=re(_);let ee=[];let te=[];for(let _ of X.nodes){te.push(_);if(_.type==="div"&&_.value===","){ee.push(te);te=[]}}ee.push(te);return ee.filter((_=>_.length>0))}stringify(_){if(_.length===0){return""}let X=[];for(let ee of _){if(ee[ee.length-1].type!=="div"){ee.push(this.div(_))}X=X.concat(ee)}if(X[0].type==="div"){X=X.slice(1)}if(X[X.length-1].type==="div"){X=X.slice(0,+-2+1||0)}return re.stringify({nodes:X})}clone(_,X,ee){let te=[];let re=false;for(let se of ee){if(!re&&se.type==="word"&&se.value===_){te.push({type:"word",value:X});re=true}else{te.push(se)}}return te}div(_){for(let X of _){for(let _ of X){if(_.type==="div"&&_.value===","){return _}}}return{type:"div",value:",",after:" "}}cleanOtherPrefixes(_,X){return _.filter((_=>{let ee=ne.prefix(this.findProp(_));return ee===""||ee===X}))}cleanFromUnprefixed(_,X){let ee=_.map((_=>this.findProp(_))).filter((_=>_.slice(0,X.length)===X)).map((_=>this.prefixes.unprefixed(_)));let te=[];for(let re of _){let _=this.findProp(re);let se=ne.prefix(_);if(!ee.includes(_)&&(se===X||se==="")){te.push(re)}}return te}disabled(_,X){let ee=["order","justify-content","align-self","align-content"];if(_.includes("flex")||ee.includes(_)){if(this.prefixes.options.flexbox===false){return true}if(this.prefixes.options.flexbox==="no-2009"){return X.includes("2009")}}return undefined}ruleVendorPrefixes(_){let{parent:X}=_;if(X.type!=="rule"){return false}else if(!X.selector.includes(":-")){return false}let ee=se.prefixes().filter((_=>X.selector.includes(":"+_)));return ee.length>0?ee:false}}_.exports=Transition},4012:(_,X,ee)=>{let{list:te}=ee(977);_.exports.error=function(_){let X=new Error(_);X.autoprefixer=true;throw X};_.exports.uniq=function(_){return[...new Set(_)]};_.exports.removeNote=function(_){if(!_.includes(" ")){return _}return _.split(" ")[0]};_.exports.escapeRegexp=function(_){return _.replace(/[$()*+-.?[\\\]^{|}]/g,"\\$&")};_.exports.regexp=function(_,X=true){if(X){_=this.escapeRegexp(_)}return new RegExp(`(^|[\\s,(])(${_}($|[\\s(,]))`,"gi")};_.exports.editList=function(_,X){let ee=te.comma(_);let re=X(ee,[]);if(ee===re){return _}let se=_.match(/,\s*/);se=se?se[0]:", ";return re.join(se)};_.exports.splitSelector=function(_){return te.comma(_).map((_=>te.space(_).map((_=>_.split(/(?=\.|#)/g)))))}},3712:(_,X,ee)=>{let te=ee(4877);let re=ee(3660);let se=ee(5965);let ne=ee(4012);class Value extends te{static save(_,X){let ee=X.prop;let te=[];for(let re in X._autoprefixerValues){let ne=X._autoprefixerValues[re];if(ne===X.value){continue}let ie;let oe=se.prefix(ee);if(oe==="-pie-"){continue}if(oe===re){ie=X.value=ne;te.push(ie);continue}let ae=_.prefixed(ee,re);let le=X.parent;if(!le.every((_=>_.prop!==ae))){te.push(ie);continue}let ue=ne.replace(/\s+/," ");let ce=le.some((_=>_.prop===X.prop&&_.value.replace(/\s+/," ")===ue));if(ce){te.push(ie);continue}let pe=this.clone(X,{value:ne});ie=X.parent.insertBefore(X,pe);te.push(ie)}return te}check(_){let X=_.value;if(!X.includes(this.name)){return false}return!!X.match(this.regexp())}regexp(){return this.regexpCache||(this.regexpCache=ne.regexp(this.name))}replace(_,X){return _.replace(this.regexp(),`$1${X}$2`)}value(_){if(_.raws.value&&_.raws.value.value===_.value){return _.raws.value.raw}else{return _.value}}add(_,X){if(!_._autoprefixerValues){_._autoprefixerValues={}}let ee=_._autoprefixerValues[X]||this.value(_);let te;do{te=ee;ee=this.replace(ee,X);if(ee===false)return}while(ee!==te);_._autoprefixerValues[X]=ee}old(_){return new re(this.name,_+this.name)}}_.exports=Value},5965:_=>{_.exports={prefix(_){let X=_.match(/^(-\w+-)/);if(X){return X[0]}return""},unprefixed(_){return _.replace(/^-\w+-/,"")}}},4442:_=>{"use strict";_.exports=balanced;function balanced(_,X,ee){if(_ instanceof RegExp)_=maybeMatch(_,ee);if(X instanceof RegExp)X=maybeMatch(X,ee);var te=range(_,X,ee);return te&&{start:te[0],end:te[1],pre:ee.slice(0,te[0]),body:ee.slice(te[0]+_.length,te[1]),post:ee.slice(te[1]+X.length)}}function maybeMatch(_,X){var ee=X.match(_);return ee?ee[0]:null}balanced.range=range;function range(_,X,ee){var te,re,se,ne,ie;var oe=ee.indexOf(_);var ae=ee.indexOf(X,oe+1);var le=oe;if(oe>=0&&ae>0){te=[];se=ee.length;while(le>=0&&!ie){if(le==oe){te.push(le);oe=ee.indexOf(_,le+1)}else if(te.length==1){ie=[te.pop(),ae]}else{re=te.pop();if(re<se){se=re;ne=ae}ae=ee.indexOf(X,le+1)}le=oe<ae&&oe>=0?oe:ae}if(te.length){ie=[se,ne]}}return ie}},441:_=>{"use strict";
/*! https://mths.be/cssesc v3.0.0 by @mathias */var X={};var ee=X.hasOwnProperty;var te=function merge(_,X){if(!_){return X}var te={};for(var re in X){te[re]=ee.call(_,re)?_[re]:X[re]}return te};var re=/[ -,\.\/:-@\[-\^`\{-~]/;var se=/[ -,\.\/:-@\[\]\^`\{-~]/;var ne=/['"\\]/;var ie=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g;var oe=function cssesc(_,X){X=te(X,cssesc.options);if(X.quotes!="single"&&X.quotes!="double"){X.quotes="single"}var ee=X.quotes=="double"?'"':"'";var ne=X.isIdentifier;var oe=_.charAt(0);var ae="";var le=0;var ue=_.length;while(le<ue){var ce=_.charAt(le++);var pe=ce.charCodeAt();var fe=void 0;if(pe<32||pe>126){if(pe>=55296&&pe<=56319&&le<ue){var de=_.charCodeAt(le++);if((de&64512)==56320){pe=((pe&1023)<<10)+(de&1023)+65536}else{le--}}fe="\\"+pe.toString(16).toUpperCase()+" "}else{if(X.escapeEverything){if(re.test(ce)){fe="\\"+ce}else{fe="\\"+pe.toString(16).toUpperCase()+" "}}else if(/[\t\n\f\r\x0B]/.test(ce)){fe="\\"+pe.toString(16).toUpperCase()+" "}else if(ce=="\\"||!ne&&(ce=='"'&&ee==ce||ce=="'"&&ee==ce)||ne&&se.test(ce)){fe="\\"+ce}else{fe=ce}}ae+=fe}if(ne){if(/^-[-\d]/.test(ae)){ae="\\-"+ae.slice(1)}else if(/\d/.test(oe)){ae="\\3"+oe+" "+ae.slice(1)}}ae=ae.replace(ie,(function(_,X,ee){if(X&&X.length%2){return _}return(X||"")+ee}));if(!ne&&X.wrap){return ee+ae+ee}return ae};oe.options={escapeEverything:false,isIdentifier:false,quotes:"single",wrap:false};oe.version="3.0.0";_.exports=oe},3227:function(_){
/**
 * @license Fraction.js v4.2.0 05/03/2022
 * https://www.xarg.org/2014/03/rational-numbers-in-javascript/
 *
 * Copyright (c) 2021, Robert Eisele (<EMAIL>)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 **/
(function(X){"use strict";var ee=2e3;var te={s:1,n:0,d:1};function assign(_,X){if(isNaN(_=parseInt(_,10))){throw Fraction["InvalidParameter"]}return _*X}function newFraction(_,X){if(X===0){throw Fraction["DivisionByZero"]}var ee=Object.create(Fraction.prototype);ee["s"]=_<0?-1:1;_=_<0?-_:_;var te=gcd(_,X);ee["n"]=_/te;ee["d"]=X/te;return ee}function factorize(_){var X={};var ee=_;var te=2;var re=4;while(re<=ee){while(ee%te===0){ee/=te;X[te]=(X[te]||0)+1}re+=1+2*te++}if(ee!==_){if(ee>1)X[ee]=(X[ee]||0)+1}else{X[_]=(X[_]||0)+1}return X}var parse=function(_,X){var ee=0,re=1,se=1;var ne=0,ie=0,oe=0,ae=1,le=1;var ue=0,ce=1;var pe=1,fe=1;var de=1e7;var he;if(_===undefined||_===null){}else if(X!==undefined){ee=_;re=X;se=ee*re;if(ee%1!==0||re%1!==0){throw Fraction["NonIntegerParameter"]}}else switch(typeof _){case"object":{if("d"in _&&"n"in _){ee=_["n"];re=_["d"];if("s"in _)ee*=_["s"]}else if(0 in _){ee=_[0];if(1 in _)re=_[1]}else{throw Fraction["InvalidParameter"]}se=ee*re;break}case"number":{if(_<0){se=_;_=-_}if(_%1===0){ee=_}else if(_>0){if(_>=1){le=Math.pow(10,Math.floor(1+Math.log(_)/Math.LN10));_/=le}while(ce<=de&&fe<=de){he=(ue+pe)/(ce+fe);if(_===he){if(ce+fe<=de){ee=ue+pe;re=ce+fe}else if(fe>ce){ee=pe;re=fe}else{ee=ue;re=ce}break}else{if(_>he){ue+=pe;ce+=fe}else{pe+=ue;fe+=ce}if(ce>de){ee=pe;re=fe}else{ee=ue;re=ce}}}ee*=le}else if(isNaN(_)||isNaN(X)){re=ee=NaN}break}case"string":{ce=_.match(/\d+|./g);if(ce===null)throw Fraction["InvalidParameter"];if(ce[ue]==="-"){se=-1;ue++}else if(ce[ue]==="+"){ue++}if(ce.length===ue+1){ie=assign(ce[ue++],se)}else if(ce[ue+1]==="."||ce[ue]==="."){if(ce[ue]!=="."){ne=assign(ce[ue++],se)}ue++;if(ue+1===ce.length||ce[ue+1]==="("&&ce[ue+3]===")"||ce[ue+1]==="'"&&ce[ue+3]==="'"){ie=assign(ce[ue],se);ae=Math.pow(10,ce[ue].length);ue++}if(ce[ue]==="("&&ce[ue+2]===")"||ce[ue]==="'"&&ce[ue+2]==="'"){oe=assign(ce[ue+1],se);le=Math.pow(10,ce[ue+1].length)-1;ue+=3}}else if(ce[ue+1]==="/"||ce[ue+1]===":"){ie=assign(ce[ue],se);ae=assign(ce[ue+2],1);ue+=3}else if(ce[ue+3]==="/"&&ce[ue+1]===" "){ne=assign(ce[ue],se);ie=assign(ce[ue+2],se);ae=assign(ce[ue+4],1);ue+=5}if(ce.length<=ue){re=ae*le;se=ee=oe+re*ne+le*ie;break}}default:throw Fraction["InvalidParameter"]}if(re===0){throw Fraction["DivisionByZero"]}te["s"]=se<0?-1:1;te["n"]=Math.abs(ee);te["d"]=Math.abs(re)};function modpow(_,X,ee){var te=1;for(;X>0;_=_*_%ee,X>>=1){if(X&1){te=te*_%ee}}return te}function cycleLen(_,X){for(;X%2===0;X/=2){}for(;X%5===0;X/=5){}if(X===1)return 0;var te=10%X;var re=1;for(;te!==1;re++){te=te*10%X;if(re>ee)return 0}return re}function cycleStart(_,X,ee){var te=1;var re=modpow(10,ee,X);for(var se=0;se<300;se++){if(te===re)return se;te=te*10%X;re=re*10%X}return 0}function gcd(_,X){if(!_)return X;if(!X)return _;while(1){_%=X;if(!_)return X;X%=_;if(!X)return _}}function Fraction(_,X){parse(_,X);if(this instanceof Fraction){_=gcd(te["d"],te["n"]);this["s"]=te["s"];this["n"]=te["n"]/_;this["d"]=te["d"]/_}else{return newFraction(te["s"]*te["n"],te["d"])}}Fraction["DivisionByZero"]=new Error("Division by Zero");Fraction["InvalidParameter"]=new Error("Invalid argument");Fraction["NonIntegerParameter"]=new Error("Parameters must be integer");Fraction.prototype={s:1,n:0,d:1,abs:function(){return newFraction(this["n"],this["d"])},neg:function(){return newFraction(-this["s"]*this["n"],this["d"])},add:function(_,X){parse(_,X);return newFraction(this["s"]*this["n"]*te["d"]+te["s"]*this["d"]*te["n"],this["d"]*te["d"])},sub:function(_,X){parse(_,X);return newFraction(this["s"]*this["n"]*te["d"]-te["s"]*this["d"]*te["n"],this["d"]*te["d"])},mul:function(_,X){parse(_,X);return newFraction(this["s"]*te["s"]*this["n"]*te["n"],this["d"]*te["d"])},div:function(_,X){parse(_,X);return newFraction(this["s"]*te["s"]*this["n"]*te["d"],this["d"]*te["n"])},clone:function(){return newFraction(this["s"]*this["n"],this["d"])},mod:function(_,X){if(isNaN(this["n"])||isNaN(this["d"])){return new Fraction(NaN)}if(_===undefined){return newFraction(this["s"]*this["n"]%this["d"],1)}parse(_,X);if(0===te["n"]&&0===this["d"]){throw Fraction["DivisionByZero"]}return newFraction(this["s"]*(te["d"]*this["n"])%(te["n"]*this["d"]),te["d"]*this["d"])},gcd:function(_,X){parse(_,X);return newFraction(gcd(te["n"],this["n"])*gcd(te["d"],this["d"]),te["d"]*this["d"])},lcm:function(_,X){parse(_,X);if(te["n"]===0&&this["n"]===0){return newFraction(0,1)}return newFraction(te["n"]*this["n"],gcd(te["n"],this["n"])*gcd(te["d"],this["d"]))},ceil:function(_){_=Math.pow(10,_||0);if(isNaN(this["n"])||isNaN(this["d"])){return new Fraction(NaN)}return newFraction(Math.ceil(_*this["s"]*this["n"]/this["d"]),_)},floor:function(_){_=Math.pow(10,_||0);if(isNaN(this["n"])||isNaN(this["d"])){return new Fraction(NaN)}return newFraction(Math.floor(_*this["s"]*this["n"]/this["d"]),_)},round:function(_){_=Math.pow(10,_||0);if(isNaN(this["n"])||isNaN(this["d"])){return new Fraction(NaN)}return newFraction(Math.round(_*this["s"]*this["n"]/this["d"]),_)},inverse:function(){return newFraction(this["s"]*this["d"],this["n"])},pow:function(_,X){parse(_,X);if(te["d"]===1){if(te["s"]<0){return newFraction(Math.pow(this["s"]*this["d"],te["n"]),Math.pow(this["n"],te["n"]))}else{return newFraction(Math.pow(this["s"]*this["n"],te["n"]),Math.pow(this["d"],te["n"]))}}if(this["s"]<0)return null;var ee=factorize(this["n"]);var re=factorize(this["d"]);var se=1;var ne=1;for(var ie in ee){if(ie==="1")continue;if(ie==="0"){se=0;break}ee[ie]*=te["n"];if(ee[ie]%te["d"]===0){ee[ie]/=te["d"]}else return null;se*=Math.pow(ie,ee[ie])}for(var ie in re){if(ie==="1")continue;re[ie]*=te["n"];if(re[ie]%te["d"]===0){re[ie]/=te["d"]}else return null;ne*=Math.pow(ie,re[ie])}if(te["s"]<0){return newFraction(ne,se)}return newFraction(se,ne)},equals:function(_,X){parse(_,X);return this["s"]*this["n"]*te["d"]===te["s"]*te["n"]*this["d"]},compare:function(_,X){parse(_,X);var ee=this["s"]*this["n"]*te["d"]-te["s"]*te["n"]*this["d"];return(0<ee)-(ee<0)},simplify:function(_){if(isNaN(this["n"])||isNaN(this["d"])){return this}_=_||.001;var X=this["abs"]();var ee=X["toContinued"]();for(var te=1;te<ee.length;te++){var re=newFraction(ee[te-1],1);for(var se=te-2;se>=0;se--){re=re["inverse"]()["add"](ee[se])}if(re["sub"](X)["abs"]().valueOf()<_){return re["mul"](this["s"])}}return this},divisible:function(_,X){parse(_,X);return!(!(te["n"]*this["d"])||this["n"]*te["d"]%(te["n"]*this["d"]))},valueOf:function(){return this["s"]*this["n"]/this["d"]},toFraction:function(_){var X,ee="";var te=this["n"];var re=this["d"];if(this["s"]<0){ee+="-"}if(re===1){ee+=te}else{if(_&&(X=Math.floor(te/re))>0){ee+=X;ee+=" ";te%=re}ee+=te;ee+="/";ee+=re}return ee},toLatex:function(_){var X,ee="";var te=this["n"];var re=this["d"];if(this["s"]<0){ee+="-"}if(re===1){ee+=te}else{if(_&&(X=Math.floor(te/re))>0){ee+=X;te%=re}ee+="\\frac{";ee+=te;ee+="}{";ee+=re;ee+="}"}return ee},toContinued:function(){var _;var X=this["n"];var ee=this["d"];var te=[];if(isNaN(X)||isNaN(ee)){return te}do{te.push(Math.floor(X/ee));_=X%ee;X=ee;ee=_}while(X!==1);return te},toString:function(_){var X=this["n"];var ee=this["d"];if(isNaN(X)||isNaN(ee)){return"NaN"}_=_||15;var te=cycleLen(X,ee);var re=cycleStart(X,ee,te);var se=this["s"]<0?"-":"";se+=X/ee|0;X%=ee;X*=10;if(X)se+=".";if(te){for(var ne=re;ne--;){se+=X/ee|0;X%=ee;X*=10}se+="(";for(var ne=te;ne--;){se+=X/ee|0;X%=ee;X*=10}se+=")"}else{for(var ne=_;X&&ne--;){se+=X/ee|0;X%=ee;X*=10}}return se}};if(typeof define==="function"&&define["amd"]){define([],(function(){return Fraction}))}else if(true){Object.defineProperty(Fraction,"__esModule",{value:true});Fraction["default"]=Fraction;Fraction["Fraction"]=Fraction;_["exports"]=Fraction}else{}})(this)},2443:_=>{"use strict";_.exports={wrap:wrapRange,limit:limitRange,validate:validateRange,test:testRange,curry:curry,name:name};function wrapRange(_,X,ee){var te=X-_;return((ee-_)%te+te)%te+_}function limitRange(_,X,ee){return Math.max(_,Math.min(X,ee))}function validateRange(_,X,ee,te,re){if(!testRange(_,X,ee,te,re)){throw new Error(ee+" is outside of range ["+_+","+X+")")}return ee}function testRange(_,X,ee,te,re){return!(ee<_||ee>X||re&&ee===X||te&&ee===_)}function name(_,X,ee,te){return(ee?"(":"[")+_+","+X+(te?")":"]")}function curry(_,X,ee,te){var re=name.bind(null,_,X,ee,te);return{wrap:wrapRange.bind(null,_,X),limit:limitRange.bind(null,_,X),validate:function(re){return validateRange(_,X,re,ee,te)},test:function(re){return testRange(_,X,re,ee,te)},toString:re,name:re}}},1437:(_,X,ee)=>{let te=process.argv||[],re=process.env;let se=!("NO_COLOR"in re||te.includes("--no-color"))&&("FORCE_COLOR"in re||te.includes("--color")||process.platform==="win32"||require!=null&&ee(6224).isatty(1)&&re.TERM!=="dumb"||"CI"in re);let formatter=(_,X,ee=_)=>te=>{let re=""+te;let se=re.indexOf(X,_.length);return~se?_+replaceClose(re,X,ee,se)+X:_+re+X};let replaceClose=(_,X,ee,te)=>{let re="";let se=0;do{re+=_.substring(se,te)+ee;se=te+X.length;te=_.indexOf(X,se)}while(~te);return re+_.substring(se)};let createColors=(_=se)=>{let X=_?formatter:()=>String;return{isColorSupported:_,reset:X("[0m","[0m"),bold:X("[1m","[22m","[22m[1m"),dim:X("[2m","[22m","[22m[2m"),italic:X("[3m","[23m"),underline:X("[4m","[24m"),inverse:X("[7m","[27m"),hidden:X("[8m","[28m"),strikethrough:X("[9m","[29m"),black:X("[30m","[39m"),red:X("[31m","[39m"),green:X("[32m","[39m"),yellow:X("[33m","[39m"),blue:X("[34m","[39m"),magenta:X("[35m","[39m"),cyan:X("[36m","[39m"),white:X("[37m","[39m"),gray:X("[90m","[39m"),bgBlack:X("[40m","[49m"),bgRed:X("[41m","[49m"),bgGreen:X("[42m","[49m"),bgYellow:X("[43m","[49m"),bgBlue:X("[44m","[49m"),bgMagenta:X("[45m","[49m"),bgCyan:X("[46m","[49m"),bgWhite:X("[47m","[49m")}};_.exports=createColors();_.exports.createColors=createColors},6924:(_,X,ee)=>{const te=ee(5418);function nodeIsInsensitiveAttribute(_){return _.type==="attribute"&&_.insensitive}function selectorHasInsensitiveAttribute(_){return _.some(nodeIsInsensitiveAttribute)}function transformString(_,X,ee){const te=ee.charAt(X);if(te===""){return _}let re=_.map((_=>_+te));const se=te.toLocaleUpperCase();if(se!==te){re=re.concat(_.map((_=>_+se)))}return transformString(re,X+1,ee)}function createSensitiveAtributes(_){const X=transformString([""],0,_.value);return X.map((X=>{const ee=_.clone({spaces:{after:_.spaces.after,before:_.spaces.before},insensitive:false});ee.setValue(X);return ee}))}function createNewSelectors(_){let X=[te.selector()];_.walk((_=>{if(!nodeIsInsensitiveAttribute(_)){X.forEach((X=>{X.append(_.clone())}));return}const ee=createSensitiveAtributes(_);const te=[];ee.forEach((_=>{X.forEach((X=>{const ee=X.clone();ee.append(_);te.push(ee)}))}));X=te}));return X}function transform(_){let X=[];_.each((_=>{if(selectorHasInsensitiveAttribute(_)){X=X.concat(createNewSelectors(_));_.remove()}}));if(X.length){X.forEach((X=>_.append(X)))}}const re=/i(\s*\/\*[\W\w]*?\*\/)*\s*\]/;_.exports=()=>({postcssPlugin:"postcss-attribute-case-insensitive",Rule(_){if(re.test(_.selector)){_.selector=te(transform).processSync(_.selector)}}});_.exports.postcss=true},4719:(_,X,ee)=>{let te=ee(2045);function parseValue(_){let X=_.match(/([\d.-]+)(.*)/);if(!X||!X[1]||!X[2]||isNaN(X[1])){return undefined}return[parseFloat(X[1]),X[2]]}function compose(_,X,ee){if(_&&X&&ee){return`max(${_}, min(${X}, ${ee}))`}if(_&&X){return`max(${_}, ${X})`}return _}function updateValue(_,X,ee){let re=X;let se=te(X);let ne=te(_.value);let ie=false;ne.walk(((_,X,ee)=>{let te=_.type==="function"&&_.value==="clamp";if(!te||ie){return}ie=true;ee[X]=se}));if(ie){re=ne.toString()}if(ee){_.cloneBefore({value:re})}else{_.value=re}}_.exports=_=>{_=_||{};let X=_.precalculate?Boolean(_.precalculate):false;let ee=_.preserve?Boolean(_.preserve):false;return{postcssPlugin:"postcss-clamp",Declaration(_){if(!_||!_.value.includes("clamp")){return}te(_.value).walk((re=>{let se=re.nodes;if(re.type!=="function"||re.value!=="clamp"||se.length!==5){return}let ne=se[0];let ie=se[2];let oe=se[4];let ae=compose(te.stringify(ne),te.stringify(ie),te.stringify(oe));if(!X||ie.type!=="word"||oe.type!=="word"){updateValue(_,ae,ee);return}let le=parseValue(ie.value);let ue=parseValue(oe.value);if(le===undefined||ue===undefined){updateValue(_,ae,ee);return}let[ce,pe]=le;let[fe,de]=ue;if(pe!==de){updateValue(_,ae,ee);return}let he=parseValue(ne.value);if(he===undefined){let X=`${ce+fe}${pe}`;updateValue(_,compose(te.stringify(ne),X),ee);return}let[me,ge]=he;if(ge!==pe){let X=`${ce+fe}${pe}`;updateValue(_,compose(te.stringify(ne),X),ee);return}updateValue(_,compose(`${me+ce+fe}${pe}`),ee)}))}}};_.exports.postcss=true},5671:(_,X,ee)=>{"use strict";var te=ee(7147);var re=ee(1017);var se=ee(977);function _interopDefaultLegacy(_){return _&&typeof _==="object"&&"default"in _?_:{default:_}}function _interopNamespace(_){if(_&&_.__esModule)return _;var X=Object.create(null);if(_){Object.keys(_).forEach((function(ee){if(ee!=="default"){var te=Object.getOwnPropertyDescriptor(_,ee);Object.defineProperty(X,ee,te.get?te:{enumerable:true,get:function(){return _[ee]}})}}))}X["default"]=_;return Object.freeze(X)}var ne=_interopDefaultLegacy(te);var ie=_interopDefaultLegacy(re);function parse(_,X){const ee=[];let te="";let re=false;let se=0;let ne=-1;while(++ne<_.length){const ie=_[ne];if(ie==="("){se+=1}else if(ie===")"){if(se>0){se-=1}}else if(se===0){if(X&&de.test(te+ie)){re=true}else if(!X&&ie===","){re=true}}if(re){ee.push(X?new MediaExpression(te+ie):new MediaQuery(te));te="";re=false}else{te+=ie}}if(te!==""){ee.push(X?new MediaExpression(te):new MediaQuery(te))}return ee}class MediaQueryList{constructor(_){this.nodes=parse(_)}invert(){this.nodes.forEach((_=>{_.invert()}));return this}clone(){return new MediaQueryList(String(this))}toString(){return this.nodes.join(",")}}class MediaQuery{constructor(_){const[,X,ee,te]=_.match(he);const[,re="",se=" ",ne="",ie="",oe="",ae="",le="",ue=""]=ee.match(me)||[];const ce={before:X,after:te,afterModifier:se,originalModifier:re||"",beforeAnd:ie,and:oe,beforeExpression:ae};const pe=parse(le||ue,true);Object.assign(this,{modifier:re,type:ne,raws:ce,nodes:pe})}clone(_){const X=new MediaQuery(String(this));Object.assign(X,_);return X}invert(){this.modifier=this.modifier?"":this.raws.originalModifier;return this}toString(){const{raws:_}=this;return`${_.before}${this.modifier}${this.modifier?`${_.afterModifier}`:""}${this.type}${_.beforeAnd}${_.and}${_.beforeExpression}${this.nodes.join("")}${this.raws.after}`}}class MediaExpression{constructor(_){const[,X,ee="",te="",re=""]=_.match(de)||[null,_];const se={after:ee,and:te,afterAnd:re};Object.assign(this,{value:X,raws:se})}clone(_){const X=new MediaExpression(String(this));Object.assign(X,_);return X}toString(){const{raws:_}=this;return`${this.value}${_.after}${_.and}${_.afterAnd}`}}const oe="(not|only)";const ae="(all|print|screen|speech)";const le="([\\W\\w]*)";const ue="([\\W\\w]+)";const ce="(\\s*)";const pe="(\\s+)";const fe="(?:(\\s+)(and))";const de=new RegExp(`^${ue}(?:${fe}${pe})$`,"i");const he=new RegExp(`^${ce}${le}${ce}$`);const me=new RegExp(`^(?:${oe}${pe})?(?:${ae}(?:${fe}${pe}${ue})?|${ue})$`,"i");var mediaASTFromString=_=>new MediaQueryList(_);var getCustomMediaFromRoot=(_,X)=>{const ee={};_.nodes.slice().forEach((_=>{if(isCustomMedia(_)){const[,te,re]=_.params.match(be);ee[te]=mediaASTFromString(re);if(!Object(X).preserve){_.remove()}}}));return ee};const ge=/^custom-media$/i;const be=/^(--[A-z][\w-]*)\s+([\W\w]+)\s*$/;const isCustomMedia=_=>_.type==="atrule"&&ge.test(_.name)&&be.test(_.params);async function getCustomMediaFromCSSFile(_){const X=await readFile(_);const ee=se.parse(X,{from:_});return getCustomMediaFromRoot(ee,{preserve:true})}function getCustomMediaFromObject(_){const X=Object.assign({},Object(_).customMedia,Object(_)["custom-media"]);for(const _ in X){X[_]=mediaASTFromString(X[_])}return X}async function getCustomMediaFromJSONFile(_){const X=await readJSON(_);return getCustomMediaFromObject(X)}async function getCustomMediaFromJSFile(_){const X=await Promise.resolve().then((function(){return _interopNamespace(require(_))}));return getCustomMediaFromObject(X)}function getCustomMediaFromSources(_){return _.map((_=>{if(_ instanceof Promise){return _}else if(_ instanceof Function){return _()}const X=_===Object(_)?_:{from:String(_)};if(Object(X).customMedia||Object(X)["custom-media"]){return X}const ee=ie["default"].resolve(String(X.from||""));const te=(X.type||ie["default"].extname(ee).slice(1)).toLowerCase();return{type:te,from:ee}})).reduce((async(_,X)=>{const{type:ee,from:te}=await X;if(ee==="css"||ee==="pcss"){return Object.assign(await _,await getCustomMediaFromCSSFile(te))}if(ee==="js"){return Object.assign(await _,await getCustomMediaFromJSFile(te))}if(ee==="json"){return Object.assign(await _,await getCustomMediaFromJSONFile(te))}return Object.assign(await _,getCustomMediaFromObject(await X))}),{})}const readFile=_=>new Promise(((X,ee)=>{ne["default"].readFile(_,"utf8",((_,te)=>{if(_){ee(_)}else{X(te)}}))}));const readJSON=async _=>JSON.parse(await readFile(_));function transformMediaList(_,X){let ee=_.nodes.length-1;while(ee>=0){const te=transformMedia(_.nodes[ee],X);if(te.length){_.nodes.splice(ee,1,...te)}--ee}return _}function transformMedia(_,X){const ee=[];for(const te in _.nodes){const{value:re,nodes:se}=_.nodes[te];const ne=re.replace(ve,"$1");if(ne in X){for(const re of X[ne].nodes){const se=_.modifier!==re.modifier?_.modifier||re.modifier:"";const ie=_.clone({modifier:se,raws:!se||_.modifier?{..._.raws}:{...re.raws},type:_.type||re.type});if(ie.type===re.type){Object.assign(ie.raws,{and:re.raws.and,beforeAnd:re.raws.beforeAnd,beforeExpression:re.raws.beforeExpression})}ie.nodes.splice(te,1,...re.clone().nodes.map((X=>{if(_.nodes[te].raws.and){X.raws={..._.nodes[te].raws}}X.spaces={..._.nodes[te].spaces};return X})));const oe=getCustomMediasWithoutKey(X,ne);const ae=transformMedia(ie,oe);if(ae.length){ee.push(...ae)}else{ee.push(ie)}}return ee}else if(se&&se.length){transformMediaList(_.nodes[te],X)}}return ee}const ve=/\((--[A-z][\w-]*)\)/;const getCustomMediasWithoutKey=(_,X)=>{const ee=Object.assign({},_);delete ee[X];return ee};var transformAtrules=(_,X,ee)=>{_.walkAtRules(ye,(_=>{if(we.test(_.params)){const te=mediaASTFromString(_.params);const re=String(transformMediaList(te,X));if(ee.preserve){_.cloneBefore({params:re})}else{_.params=re}}}))};const ye=/^media$/i;const we=/\(--[A-z][\w-]*\)/;async function writeCustomMediaToCssFile(_,X){const ee=Object.keys(X).reduce(((_,ee)=>{_.push(`@custom-media ${ee} ${X[ee]};`);return _}),[]).join("\n");const te=`${ee}\n`;await writeFile(_,te)}async function writeCustomMediaToJsonFile(_,X){const ee=JSON.stringify({"custom-media":X},null,"  ");const te=`${ee}\n`;await writeFile(_,te)}async function writeCustomMediaToCjsFile(_,X){const ee=Object.keys(X).reduce(((_,ee)=>{_.push(`\t\t'${escapeForJS(ee)}': '${escapeForJS(X[ee])}'`);return _}),[]).join(",\n");const te=`module.exports = {\n\tcustomMedia: {\n${ee}\n\t}\n};\n`;await writeFile(_,te)}async function writeCustomMediaToMjsFile(_,X){const ee=Object.keys(X).reduce(((_,ee)=>{_.push(`\t'${escapeForJS(ee)}': '${escapeForJS(X[ee])}'`);return _}),[]).join(",\n");const te=`export const customMedia = {\n${ee}\n};\n`;await writeFile(_,te)}function writeCustomMediaToExports(_,X){return Promise.all(X.map((async X=>{if(X instanceof Function){await X(defaultCustomMediaToJSON(_))}else{const ee=X===Object(X)?X:{to:String(X)};const te=ee.toJSON||defaultCustomMediaToJSON;if("customMedia"in ee){ee.customMedia=te(_)}else if("custom-media"in ee){ee["custom-media"]=te(_)}else{const X=String(ee.to||"");const re=(ee.type||ie["default"].extname(X).slice(1)).toLowerCase();const se=te(_);if(re==="css"){await writeCustomMediaToCssFile(X,se)}if(re==="js"){await writeCustomMediaToCjsFile(X,se)}if(re==="json"){await writeCustomMediaToJsonFile(X,se)}if(re==="mjs"){await writeCustomMediaToMjsFile(X,se)}}}})))}const defaultCustomMediaToJSON=_=>Object.keys(_).reduce(((X,ee)=>{X[ee]=String(_[ee]);return X}),{});const writeFile=(_,X)=>new Promise(((ee,te)=>{ne["default"].writeFile(_,X,(_=>{if(_){te(_)}else{ee()}}))}));const escapeForJS=_=>_.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r");const creator=_=>{const X="preserve"in Object(_)?Boolean(_.preserve):false;const ee=[].concat(Object(_).importFrom||[]);const te=[].concat(Object(_).exportTo||[]);const re=getCustomMediaFromSources(ee);return{postcssPlugin:"postcss-custom-media",Once:async _=>{const ee=Object.assign(await re,getCustomMediaFromRoot(_,{preserve:X}));await writeCustomMediaToExports(ee,te);transformAtrules(_,ee,{preserve:X})}}};creator.postcss=true;_.exports=creator},8179:(_,X,ee)=>{"use strict";var te=ee(5418);var re=ee(7147);var se=ee(1017);var ne=ee(977);function _interopDefaultLegacy(_){return _&&typeof _==="object"&&"default"in _?_:{default:_}}function _interopNamespace(_){if(_&&_.__esModule)return _;var X=Object.create(null);if(_){Object.keys(_).forEach((function(ee){if(ee!=="default"){var te=Object.getOwnPropertyDescriptor(_,ee);Object.defineProperty(X,ee,te.get?te:{enumerable:true,get:function(){return _[ee]}})}}))}X["default"]=_;return Object.freeze(X)}var ie=_interopDefaultLegacy(te);var oe=_interopDefaultLegacy(re);var ae=_interopDefaultLegacy(se);var le=_interopDefaultLegacy(ne);var getSelectorsAstFromSelectorsString=_=>{let X;ie["default"]((_=>{X=_})).processSync(_);return X};var getCustomSelectors=(_,X)=>{const ee={};_.nodes.slice().forEach((_=>{if(isCustomSelector(_)){const[,te,re]=_.params.match(ce);ee[te]=getSelectorsAstFromSelectorsString(re);if(!Object(X).preserve){_.remove()}}}));return ee};const ue=/^custom-selector$/i;const ce=/^(:--[A-z][\w-]*)\s+([\W\w]+)\s*$/;const isCustomSelector=_=>_.type==="atrule"&&ue.test(_.name)&&ce.test(_.params);function transformSelectorList(_,X){let ee=_.nodes.length-1;while(ee>=0){const te=transformSelector(_.nodes[ee],X);if(te.length){_.nodes.splice(ee,1,...te)}--ee}return _}function transformSelector(_,X){const ee=[];for(const te in _.nodes){const{value:re,nodes:se}=_.nodes[te];if(re in X){for(const se of X[re].nodes){const re=_.clone();re.nodes.splice(te,1,...se.clone().nodes.map((X=>{X.spaces={..._.nodes[te].spaces};return X})));const ne=transformSelector(re,X);adjustNodesBySelectorEnds(re.nodes,Number(te));if(ne.length){ee.push(...ne)}else{ee.push(re)}}return ee}else if(se&&se.length){transformSelectorList(_.nodes[te],X)}}return ee}const pe=/^(tag|universal)$/;const fe=/^(class|id|pseudo|tag|universal)$/;const isWithoutSelectorStart=_=>pe.test(Object(_).type);const isWithoutSelectorEnd=_=>fe.test(Object(_).type);const adjustNodesBySelectorEnds=(_,X)=>{if(X&&isWithoutSelectorStart(_[X])&&isWithoutSelectorEnd(_[X-1])){let ee=X-1;while(ee&&isWithoutSelectorEnd(_[ee])){--ee}if(ee<X){const te=_.splice(X,1)[0];_.splice(ee,0,te);_[ee].spaces.before=_[ee+1].spaces.before;_[ee+1].spaces.before="";if(_[X]){_[X].spaces.after=_[ee].spaces.after;_[ee].spaces.after=""}}}};var transformRules=(_,X,ee)=>{_.walkRules(de,(_=>{const te=ie["default"]((_=>{transformSelectorList(_,X)})).processSync(_.selector);if(ee.preserve){_.cloneBefore({selector:te})}else{_.selector=te}}))};const de=/:--[A-z][\w-]*/;function importCustomSelectorsFromCSSAST(_){return getCustomSelectors(_)}async function importCustomSelectorsFromCSSFile(_){const X=await readFile(ae["default"].resolve(_));const ee=le["default"].parse(X,{from:ae["default"].resolve(_)});return importCustomSelectorsFromCSSAST(ee)}function importCustomSelectorsFromObject(_){const X=Object.assign({},Object(_).customSelectors||Object(_)["custom-selectors"]);for(const _ in X){X[_]=getSelectorsAstFromSelectorsString(X[_])}return X}async function importCustomSelectorsFromJSONFile(_){const X=await readJSON(ae["default"].resolve(_));return importCustomSelectorsFromObject(X)}async function importCustomSelectorsFromJSFile(_){const X=await Promise.resolve().then((function(){return _interopNamespace(require(ae["default"].resolve(_)))}));return importCustomSelectorsFromObject(X)}function importCustomSelectorsFromSources(_){return _.map((_=>{if(_ instanceof Promise){return _}else if(_ instanceof Function){return _()}const X=_===Object(_)?_:{from:String(_)};if(Object(X).customSelectors||Object(X)["custom-selectors"]){return X}const ee=String(X.from||"");const te=(X.type||ae["default"].extname(ee).slice(1)).toLowerCase();return{type:te,from:ee}})).reduce((async(_,X)=>{const ee=await _;const{type:te,from:re}=await X;if(te==="ast"){return Object.assign(ee,importCustomSelectorsFromCSSAST(re))}if(te==="css"){return Object.assign(ee,await importCustomSelectorsFromCSSFile(re))}if(te==="js"){return Object.assign(ee,await importCustomSelectorsFromJSFile(re))}if(te==="json"){return Object.assign(ee,await importCustomSelectorsFromJSONFile(re))}return Object.assign(ee,importCustomSelectorsFromObject(await X))}),Promise.resolve({}))}const readFile=_=>new Promise(((X,ee)=>{oe["default"].readFile(_,"utf8",((_,te)=>{if(_){ee(_)}else{X(te)}}))}));const readJSON=async _=>JSON.parse(await readFile(_));async function exportCustomSelectorsToCssFile(_,X){const ee=Object.keys(X).reduce(((_,ee)=>{_.push(`@custom-selector ${ee} ${X[ee]};`);return _}),[]).join("\n");const te=`${ee}\n`;await writeFile(_,te)}async function exportCustomSelectorsToJsonFile(_,X){const ee=JSON.stringify({"custom-selectors":X},null,"  ");const te=`${ee}\n`;await writeFile(_,te)}async function exportCustomSelectorsToCjsFile(_,X){const ee=Object.keys(X).reduce(((_,ee)=>{_.push(`\t\t'${escapeForJS(ee)}': '${escapeForJS(X[ee])}'`);return _}),[]).join(",\n");const te=`module.exports = {\n\tcustomSelectors: {\n${ee}\n\t}\n};\n`;await writeFile(_,te)}async function exportCustomSelectorsToMjsFile(_,X){const ee=Object.keys(X).reduce(((_,ee)=>{_.push(`\t'${escapeForJS(ee)}': '${escapeForJS(X[ee])}'`);return _}),[]).join(",\n");const te=`export const customSelectors = {\n${ee}\n};\n`;await writeFile(_,te)}function exportCustomSelectorsToDestinations(_,X){return Promise.all(X.map((async X=>{if(X instanceof Function){await X(defaultCustomSelectorsToJSON(_))}else{const ee=X===Object(X)?X:{to:String(X)};const te=ee.toJSON||defaultCustomSelectorsToJSON;if("customSelectors"in ee){ee.customSelectors=te(_)}else if("custom-selectors"in ee){ee["custom-selectors"]=te(_)}else{const X=String(ee.to||"");const re=(ee.type||ae["default"].extname(ee.to).slice(1)).toLowerCase();const se=te(_);if(re==="css"){await exportCustomSelectorsToCssFile(X,se)}if(re==="js"){await exportCustomSelectorsToCjsFile(X,se)}if(re==="json"){await exportCustomSelectorsToJsonFile(X,se)}if(re==="mjs"){await exportCustomSelectorsToMjsFile(X,se)}}}})))}const defaultCustomSelectorsToJSON=_=>Object.keys(_).reduce(((X,ee)=>{X[ee]=String(_[ee]);return X}),{});const writeFile=(_,X)=>new Promise(((ee,te)=>{oe["default"].writeFile(_,X,(_=>{if(_){te(_)}else{ee()}}))}));const escapeForJS=_=>_.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r");const postcssCustomSelectors=_=>{const X=Boolean(Object(_).preserve);const ee=[].concat(Object(_).importFrom||[]);const te=[].concat(Object(_).exportTo||[]);const re=importCustomSelectorsFromSources(ee);return{postcssPlugin:"postcss-custom-selectors",async Once(_){const ee=Object.assign({},await re,getCustomSelectors(_,{preserve:X}));await exportCustomSelectorsToDestinations(ee,te);transformRules(_,ee,{preserve:X})}}};postcssCustomSelectors.postcss=true;_.exports=postcssCustomSelectors},6033:_=>{const X={"font-variant-ligatures":{"common-ligatures":'"liga", "clig"',"no-common-ligatures":'"liga", "clig off"',"discretionary-ligatures":'"dlig"',"no-discretionary-ligatures":'"dlig" off',"historical-ligatures":'"hlig"',"no-historical-ligatures":'"hlig" off',contextual:'"calt"',"no-contextual":'"calt" off'},"font-variant-position":{sub:'"subs"',super:'"sups"',normal:'"subs" off, "sups" off'},"font-variant-caps":{"small-caps":'"smcp"',"all-small-caps":'"smcp", "c2sc"',"petite-caps":'"pcap"',"all-petite-caps":'"pcap", "c2pc"',unicase:'"unic"',"titling-caps":'"titl"'},"font-variant-numeric":{"lining-nums":'"lnum"',"oldstyle-nums":'"onum"',"proportional-nums":'"pnum"',"tabular-nums":'"tnum"',"diagonal-fractions":'"frac"',"stacked-fractions":'"afrc"',ordinal:'"ordn"',"slashed-zero":'"zero"'},"font-kerning":{normal:'"kern"',none:'"kern" off'},"font-variant":{normal:"normal",inherit:"inherit"}};for(const _ in X){const ee=X[_];for(const _ in ee){if(!(_ in X["font-variant"])){X["font-variant"][_]=ee[_]}}}function getFontFeatureSettingsPrevTo(_){let X=null;_.parent.walkDecls((_=>{if(_.prop==="font-feature-settings"){X=_}}));if(X===null){X=_.clone();X.prop="font-feature-settings";X.value="";_.parent.insertBefore(_,X)}return X}function walkRule(_){let ee=null;_.walkDecls((_=>{if(!X[_.prop]){return null}let te=_.value;if(_.prop==="font-variant"){te=_.value.split(/\s+/g).map((_=>X["font-variant"][_])).join(", ")}else if(X[_.prop][_.value]){te=X[_.prop][_.value]}if(ee===null){ee=getFontFeatureSettingsPrevTo(_)}if(ee.value&&ee.value!==te){ee.value+=", "+te}else{ee.value=te}}))}_.exports=()=>({postcssPlugin:"postcss-font-variant",Once(_){_.walkRules(walkRule)}});_.exports.postcss=true},8633:(_,X,ee)=>{var te=ee(298);_.exports=function postcssInitial(_){_=_||{};_.reset=_.reset||"all";_.replace=_.replace||false;var X=te(_.reset==="inherited");var getPropPrevTo=function(_,X){var ee=false;X.parent.walkDecls((function(_){if(_.prop===X.prop&&_.value!==X.value){ee=true}}));return ee};return{postcssPlugin:"postcss-initial",Declaration:function(ee){if(!/\binitial\b/.test(ee.value)){return}var te=X(ee.prop,ee.value);if(te.length===0)return;te.forEach((function(_){if(!getPropPrevTo(ee.prop,ee)){ee.cloneBefore(_)}}));if(_.replace===true){ee.remove()}}}};_.exports.postcss=true},298:(_,X,ee)=>{var te=ee(9717);function template(_,X){return _.replace(/\$\{([\w\-\.]*)\}/g,(function(_,ee){var te=X[ee];return typeof te!=="undefined"&&te!==null?te:""}))}function _getRulesMap(_){return _.filter((function(_){return!_.combined})).reduce((function(_,X){_[X.prop.replace(/\-/g,"")]=X.initial;return _}),{})}function _compileDecls(_){var X=_getRulesMap(_);return _.map((function(_){if(_.combined&&_.initial){_.initial=template(_.initial.replace(/\-/g,""),X)}return _}))}function _getRequirements(_){return _.reduce((function(_,X){if(!X.contains)return _;return X.contains.reduce((function(_,ee){_[ee]=X;return _}),_)}),{})}function _expandContainments(_){var X=_getRequirements(_);return _.filter((function(_){return!_.contains})).map((function(_){var ee=X[_.prop];if(ee){_.requiredBy=ee.prop;_.basic=_.basic||ee.basic;_.inherited=_.inherited||ee.inherited}return _}))}var re=_expandContainments(_compileDecls(te));function _clearDecls(_,X){return _.map((function(_){return{prop:_.prop,value:X.replace(/\binitial\b/g,_.initial)}}))}function _allDecls(_){return re.filter((function(X){var ee=X.combined||X.basic;if(_)return ee&&X.inherited;return ee}))}function _concreteDecl(_){return re.filter((function(X){return _===X.prop||_===X.requiredBy}))}function makeFallbackFunction(_){return function(X,ee){var te;if(X==="all"){te=_allDecls(_)}else{te=_concreteDecl(X)}return _clearDecls(te,ee)}}_.exports=makeFallbackFunction},9142:_=>{const X={width:"px",height:"px","device-width":"px","device-height":"px","aspect-ratio":"","device-aspect-ratio":"",color:"","color-index":"",monochrome:"",resolution:"dpi"};const ee=Object.keys(X);const te=.001;const re={">":1,"<":-1};const se={">":"min","<":"max"};function create_query(_,ee,ne,ie){return ie.replace(/([-\d\.]+)(.*)/,(function(ie,oe,ae){const le=parseFloat(oe);if(parseFloat(oe)||ne){if(!ne){if(ae==="px"&&le===parseInt(oe,10)){oe=le+re[ee]}else{oe=Number(Math.round(parseFloat(oe)+te*re[ee]+"e6")+"e-6")}}}else{oe=re[ee]+X[_]}return"("+se[ee]+"-"+_+": "+oe+ae+")"}))}function transform(_){if(!_.params.includes("<")&&!_.params.includes(">")){return}_.params=_.params.replace(/\(\s*([a-z-]+?)\s*([<>])(=?)\s*((?:-?\d*\.?(?:\s*\/?\s*)?\d+[a-z]*)?)\s*\)/gi,(function(_,X,te,re,se){if(ee.indexOf(X)>-1){return create_query(X,te,re,se)}return _}));_.params=_.params.replace(/\(\s*((?:-?\d*\.?(?:\s*\/?\s*)?\d+[a-z]*)?)\s*(<|>)(=?)\s*([a-z-]+)\s*(<|>)(=?)\s*((?:-?\d*\.?(?:\s*\/?\s*)?\d+[a-z]*)?)\s*\)/gi,(function(_,X,te,re,se,ne,ie,oe){if(ee.indexOf(se)>-1){if(te==="<"&&ne==="<"||te===">"&&ne===">"){const _=te==="<"?X:oe;const ee=te==="<"?oe:X;let ne=re;let ae=ie;if(te===">"){ne=ie;ae=re}return create_query(se,">",ne,_)+" and "+create_query(se,"<",ae,ee)}}return _}))}_.exports=()=>({postcssPlugin:"postcss-media-minmax",AtRule:{media:_=>{transform(_)},"custom-media":_=>{transform(_)}}});_.exports.postcss=true},4658:_=>{const X=new Set(["inherit","initial","revert","unset"]);_.exports=({preserve:_=false}={})=>({postcssPlugin:"postcss-opacity-percentage",Declaration:{opacity:ee=>{if(!ee.value||ee.value.startsWith("var(")||!ee.value.endsWith("%")||X.has(ee.value)){return}ee.cloneBefore({value:String(Number.parseFloat(ee.value)/100)});if(!_){ee.remove()}}}});_.exports.postcss=true},971:_=>{_.exports=function(_){return{postcssPlugin:"postcss-page-break",Declaration(_){if(_.prop.startsWith("break-")&&/^break-(inside|before|after)/.test(_.prop)){if(_.value.search(/column|region/)>=0){return}let X;switch(_.value){case"page":X="always";break;case"avoid-page":X="avoid";break;default:X=_.value}const ee="page-"+_.prop;if(_.parent.every((_=>_.prop!==ee))){_.cloneBefore({prop:ee,value:X})}}}}};_.exports.postcss=true},3181:_=>{_.exports=function(_){_=_||{};var X=_.method||"replace";return{postcssPlugin:"postcss-replace-overflow-wrap",Declaration:{"overflow-wrap":_=>{_.cloneBefore({prop:"word-wrap"});if(X==="replace"){_.remove()}}}}};_.exports.postcss=true},3991:(_,X,ee)=>{"use strict";Object.defineProperty(X,"__esModule",{value:true});X["default"]=void 0;var te=_interopRequireDefault(ee(5726));var re=_interopRequireDefault(ee(4442));function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function explodeSelector(_,X){const ee=locatePseudoClass(X,_);if(X&&ee>-1){const se=X.slice(0,ee);const ne=(0,re.default)("(",")",X.slice(ee));if(!ne){return X}const ie=ne.body?te.default.comma(ne.body).map((X=>explodeSelector(_,X))).join(`)${_}(`):"";const oe=ne.post?explodeSelector(_,ne.post):"";return`${se}${_}(${ie})${oe}`}return X}const se={};function locatePseudoClass(_,X){se[X]=se[X]||new RegExp(`([^\\\\]|^)${X}`);const ee=se[X];const te=_.search(ee);if(te===-1){return-1}return te+_.slice(te).indexOf(X)}function explodeSelectors(_){return()=>({postcssPlugin:"postcss-selector-not",Rule:X=>{if(X.selector&&X.selector.indexOf(_)>-1){X.selector=explodeSelector(_,X.selector)}}})}const ne=explodeSelectors(":not");ne.postcss=true;var ie=ne;X["default"]=ie},5418:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6528));var re=_interopRequireWildcard(ee(3110));function _getRequireWildcardCache(){if(typeof WeakMap!=="function")return null;var _=new WeakMap;_getRequireWildcardCache=function _getRequireWildcardCache(){return _};return _}function _interopRequireWildcard(_){if(_&&_.__esModule){return _}if(_===null||typeof _!=="object"&&typeof _!=="function"){return{default:_}}var X=_getRequireWildcardCache();if(X&&X.has(_)){return X.get(_)}var ee={};var te=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var re in _){if(Object.prototype.hasOwnProperty.call(_,re)){var se=te?Object.getOwnPropertyDescriptor(_,re):null;if(se&&(se.get||se.set)){Object.defineProperty(ee,re,se)}else{ee[re]=_[re]}}}ee["default"]=_;if(X){X.set(_,ee)}return ee}function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}var se=function parser(_){return new te["default"](_)};Object.assign(se,re);delete se.__esModule;var ne=se;X["default"]=ne;_.exports=X.default},6305:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(422));var re=_interopRequireDefault(ee(5013));var se=_interopRequireDefault(ee(6870));var ne=_interopRequireDefault(ee(5047));var ie=_interopRequireDefault(ee(8393));var oe=_interopRequireDefault(ee(9443));var ae=_interopRequireDefault(ee(435));var le=_interopRequireDefault(ee(5326));var ue=_interopRequireWildcard(ee(9248));var ce=_interopRequireDefault(ee(1165));var pe=_interopRequireDefault(ee(2537));var fe=_interopRequireDefault(ee(6060));var de=_interopRequireDefault(ee(2173));var he=_interopRequireWildcard(ee(2133));var me=_interopRequireWildcard(ee(8553));var ge=_interopRequireWildcard(ee(8600));var be=ee(4513);var ve,ye;function _getRequireWildcardCache(){if(typeof WeakMap!=="function")return null;var _=new WeakMap;_getRequireWildcardCache=function _getRequireWildcardCache(){return _};return _}function _interopRequireWildcard(_){if(_&&_.__esModule){return _}if(_===null||typeof _!=="object"&&typeof _!=="function"){return{default:_}}var X=_getRequireWildcardCache();if(X&&X.has(_)){return X.get(_)}var ee={};var te=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var re in _){if(Object.prototype.hasOwnProperty.call(_,re)){var se=te?Object.getOwnPropertyDescriptor(_,re):null;if(se&&(se.get||se.set)){Object.defineProperty(ee,re,se)}else{ee[re]=_[re]}}}ee["default"]=_;if(X){X.set(_,ee)}return ee}function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _defineProperties(_,X){for(var ee=0;ee<X.length;ee++){var te=X[ee];te.enumerable=te.enumerable||false;te.configurable=true;if("value"in te)te.writable=true;Object.defineProperty(_,te.key,te)}}function _createClass(_,X,ee){if(X)_defineProperties(_.prototype,X);if(ee)_defineProperties(_,ee);return _}var we=(ve={},ve[me.space]=true,ve[me.cr]=true,ve[me.feed]=true,ve[me.newline]=true,ve[me.tab]=true,ve);var xe=Object.assign({},we,(ye={},ye[me.comment]=true,ye));function tokenStart(_){return{line:_[he.FIELDS.START_LINE],column:_[he.FIELDS.START_COL]}}function tokenEnd(_){return{line:_[he.FIELDS.END_LINE],column:_[he.FIELDS.END_COL]}}function getSource(_,X,ee,te){return{start:{line:_,column:X},end:{line:ee,column:te}}}function getTokenSource(_){return getSource(_[he.FIELDS.START_LINE],_[he.FIELDS.START_COL],_[he.FIELDS.END_LINE],_[he.FIELDS.END_COL])}function getTokenSourceSpan(_,X){if(!_){return undefined}return getSource(_[he.FIELDS.START_LINE],_[he.FIELDS.START_COL],X[he.FIELDS.END_LINE],X[he.FIELDS.END_COL])}function unescapeProp(_,X){var ee=_[X];if(typeof ee!=="string"){return}if(ee.indexOf("\\")!==-1){(0,be.ensureObject)(_,"raws");_[X]=(0,be.unesc)(ee);if(_.raws[X]===undefined){_.raws[X]=ee}}return _}function indexesOf(_,X){var ee=-1;var te=[];while((ee=_.indexOf(X,ee+1))!==-1){te.push(ee)}return te}function uniqs(){var _=Array.prototype.concat.apply([],arguments);return _.filter((function(X,ee){return ee===_.indexOf(X)}))}var ke=function(){function Parser(_,X){if(X===void 0){X={}}this.rule=_;this.options=Object.assign({lossy:false,safe:false},X);this.position=0;this.css=typeof this.rule==="string"?this.rule:this.rule.selector;this.tokens=(0,he["default"])({css:this.css,error:this._errorGenerator(),safe:this.options.safe});var ee=getTokenSourceSpan(this.tokens[0],this.tokens[this.tokens.length-1]);this.root=new te["default"]({source:ee});this.root.errorGenerator=this._errorGenerator();var se=new re["default"]({source:{start:{line:1,column:1}}});this.root.append(se);this.current=se;this.loop()}var _=Parser.prototype;_._errorGenerator=function _errorGenerator(){var _=this;return function(X,ee){if(typeof _.rule==="string"){return new Error(X)}return _.rule.error(X,ee)}};_.attribute=function attribute(){var _=[];var X=this.currToken;this.position++;while(this.position<this.tokens.length&&this.currToken[he.FIELDS.TYPE]!==me.closeSquare){_.push(this.currToken);this.position++}if(this.currToken[he.FIELDS.TYPE]!==me.closeSquare){return this.expected("closing square bracket",this.currToken[he.FIELDS.START_POS])}var ee=_.length;var te={source:getSource(X[1],X[2],this.currToken[3],this.currToken[4]),sourceIndex:X[he.FIELDS.START_POS]};if(ee===1&&!~[me.word].indexOf(_[0][he.FIELDS.TYPE])){return this.expected("attribute",_[0][he.FIELDS.START_POS])}var re=0;var se="";var ne="";var ie=null;var oe=false;while(re<ee){var ae=_[re];var le=this.content(ae);var ce=_[re+1];switch(ae[he.FIELDS.TYPE]){case me.space:oe=true;if(this.options.lossy){break}if(ie){(0,be.ensureObject)(te,"spaces",ie);var pe=te.spaces[ie].after||"";te.spaces[ie].after=pe+le;var fe=(0,be.getProp)(te,"raws","spaces",ie,"after")||null;if(fe){te.raws.spaces[ie].after=fe+le}}else{se=se+le;ne=ne+le}break;case me.asterisk:if(ce[he.FIELDS.TYPE]===me.equals){te.operator=le;ie="operator"}else if((!te.namespace||ie==="namespace"&&!oe)&&ce){if(se){(0,be.ensureObject)(te,"spaces","attribute");te.spaces.attribute.before=se;se=""}if(ne){(0,be.ensureObject)(te,"raws","spaces","attribute");te.raws.spaces.attribute.before=se;ne=""}te.namespace=(te.namespace||"")+le;var de=(0,be.getProp)(te,"raws","namespace")||null;if(de){te.raws.namespace+=le}ie="namespace"}oe=false;break;case me.dollar:if(ie==="value"){var ge=(0,be.getProp)(te,"raws","value");te.value+="$";if(ge){te.raws.value=ge+"$"}break}case me.caret:if(ce[he.FIELDS.TYPE]===me.equals){te.operator=le;ie="operator"}oe=false;break;case me.combinator:if(le==="~"&&ce[he.FIELDS.TYPE]===me.equals){te.operator=le;ie="operator"}if(le!=="|"){oe=false;break}if(ce[he.FIELDS.TYPE]===me.equals){te.operator=le;ie="operator"}else if(!te.namespace&&!te.attribute){te.namespace=true}oe=false;break;case me.word:if(ce&&this.content(ce)==="|"&&_[re+2]&&_[re+2][he.FIELDS.TYPE]!==me.equals&&!te.operator&&!te.namespace){te.namespace=le;ie="namespace"}else if(!te.attribute||ie==="attribute"&&!oe){if(se){(0,be.ensureObject)(te,"spaces","attribute");te.spaces.attribute.before=se;se=""}if(ne){(0,be.ensureObject)(te,"raws","spaces","attribute");te.raws.spaces.attribute.before=ne;ne=""}te.attribute=(te.attribute||"")+le;var ve=(0,be.getProp)(te,"raws","attribute")||null;if(ve){te.raws.attribute+=le}ie="attribute"}else if(!te.value&&te.value!==""||ie==="value"&&!(oe||te.quoteMark)){var ye=(0,be.unesc)(le);var we=(0,be.getProp)(te,"raws","value")||"";var xe=te.value||"";te.value=xe+ye;te.quoteMark=null;if(ye!==le||we){(0,be.ensureObject)(te,"raws");te.raws.value=(we||xe)+le}ie="value"}else{var ke=le==="i"||le==="I";if((te.value||te.value==="")&&(te.quoteMark||oe)){te.insensitive=ke;if(!ke||le==="I"){(0,be.ensureObject)(te,"raws");te.raws.insensitiveFlag=le}ie="insensitive";if(se){(0,be.ensureObject)(te,"spaces","insensitive");te.spaces.insensitive.before=se;se=""}if(ne){(0,be.ensureObject)(te,"raws","spaces","insensitive");te.raws.spaces.insensitive.before=ne;ne=""}}else if(te.value||te.value===""){ie="value";te.value+=le;if(te.raws.value){te.raws.value+=le}}}oe=false;break;case me.str:if(!te.attribute||!te.operator){return this.error("Expected an attribute followed by an operator preceding the string.",{index:ae[he.FIELDS.START_POS]})}var Se=(0,ue.unescapeValue)(le),_e=Se.unescaped,Pe=Se.quoteMark;te.value=_e;te.quoteMark=Pe;ie="value";(0,be.ensureObject)(te,"raws");te.raws.value=le;oe=false;break;case me.equals:if(!te.attribute){return this.expected("attribute",ae[he.FIELDS.START_POS],le)}if(te.value){return this.error('Unexpected "=" found; an operator was already defined.',{index:ae[he.FIELDS.START_POS]})}te.operator=te.operator?te.operator+le:le;ie="operator";oe=false;break;case me.comment:if(ie){if(oe||ce&&ce[he.FIELDS.TYPE]===me.space||ie==="insensitive"){var Oe=(0,be.getProp)(te,"spaces",ie,"after")||"";var je=(0,be.getProp)(te,"raws","spaces",ie,"after")||Oe;(0,be.ensureObject)(te,"raws","spaces",ie);te.raws.spaces[ie].after=je+le}else{var Te=te[ie]||"";var Ee=(0,be.getProp)(te,"raws",ie)||Te;(0,be.ensureObject)(te,"raws");te.raws[ie]=Ee+le}}else{ne=ne+le}break;default:return this.error('Unexpected "'+le+'" found.',{index:ae[he.FIELDS.START_POS]})}re++}unescapeProp(te,"attribute");unescapeProp(te,"namespace");this.newNode(new ue["default"](te));this.position++};_.parseWhitespaceEquivalentTokens=function parseWhitespaceEquivalentTokens(_){if(_<0){_=this.tokens.length}var X=this.position;var ee=[];var te="";var re=undefined;do{if(we[this.currToken[he.FIELDS.TYPE]]){if(!this.options.lossy){te+=this.content()}}else if(this.currToken[he.FIELDS.TYPE]===me.comment){var se={};if(te){se.before=te;te=""}re=new ne["default"]({value:this.content(),source:getTokenSource(this.currToken),sourceIndex:this.currToken[he.FIELDS.START_POS],spaces:se});ee.push(re)}}while(++this.position<_);if(te){if(re){re.spaces.after=te}else if(!this.options.lossy){var ie=this.tokens[X];var oe=this.tokens[this.position-1];ee.push(new ae["default"]({value:"",source:getSource(ie[he.FIELDS.START_LINE],ie[he.FIELDS.START_COL],oe[he.FIELDS.END_LINE],oe[he.FIELDS.END_COL]),sourceIndex:ie[he.FIELDS.START_POS],spaces:{before:te,after:""}}))}}return ee};_.convertWhitespaceNodesToSpace=function convertWhitespaceNodesToSpace(_,X){var ee=this;if(X===void 0){X=false}var te="";var re="";_.forEach((function(_){var se=ee.lossySpace(_.spaces.before,X);var ne=ee.lossySpace(_.rawSpaceBefore,X);te+=se+ee.lossySpace(_.spaces.after,X&&se.length===0);re+=se+_.value+ee.lossySpace(_.rawSpaceAfter,X&&ne.length===0)}));if(re===te){re=undefined}var se={space:te,rawSpace:re};return se};_.isNamedCombinator=function isNamedCombinator(_){if(_===void 0){_=this.position}return this.tokens[_+0]&&this.tokens[_+0][he.FIELDS.TYPE]===me.slash&&this.tokens[_+1]&&this.tokens[_+1][he.FIELDS.TYPE]===me.word&&this.tokens[_+2]&&this.tokens[_+2][he.FIELDS.TYPE]===me.slash};_.namedCombinator=function namedCombinator(){if(this.isNamedCombinator()){var _=this.content(this.tokens[this.position+1]);var X=(0,be.unesc)(_).toLowerCase();var ee={};if(X!==_){ee.value="/"+_+"/"}var te=new pe["default"]({value:"/"+X+"/",source:getSource(this.currToken[he.FIELDS.START_LINE],this.currToken[he.FIELDS.START_COL],this.tokens[this.position+2][he.FIELDS.END_LINE],this.tokens[this.position+2][he.FIELDS.END_COL]),sourceIndex:this.currToken[he.FIELDS.START_POS],raws:ee});this.position=this.position+3;return te}else{this.unexpected()}};_.combinator=function combinator(){var _=this;if(this.content()==="|"){return this.namespace()}var X=this.locateNextMeaningfulToken(this.position);if(X<0||this.tokens[X][he.FIELDS.TYPE]===me.comma){var ee=this.parseWhitespaceEquivalentTokens(X);if(ee.length>0){var te=this.current.last;if(te){var re=this.convertWhitespaceNodesToSpace(ee),se=re.space,ne=re.rawSpace;if(ne!==undefined){te.rawSpaceAfter+=ne}te.spaces.after+=se}else{ee.forEach((function(X){return _.newNode(X)}))}}return}var ie=this.currToken;var oe=undefined;if(X>this.position){oe=this.parseWhitespaceEquivalentTokens(X)}var ae;if(this.isNamedCombinator()){ae=this.namedCombinator()}else if(this.currToken[he.FIELDS.TYPE]===me.combinator){ae=new pe["default"]({value:this.content(),source:getTokenSource(this.currToken),sourceIndex:this.currToken[he.FIELDS.START_POS]});this.position++}else if(we[this.currToken[he.FIELDS.TYPE]]){}else if(!oe){this.unexpected()}if(ae){if(oe){var le=this.convertWhitespaceNodesToSpace(oe),ue=le.space,ce=le.rawSpace;ae.spaces.before=ue;ae.rawSpaceBefore=ce}}else{var fe=this.convertWhitespaceNodesToSpace(oe,true),de=fe.space,ge=fe.rawSpace;if(!ge){ge=de}var be={};var ve={spaces:{}};if(de.endsWith(" ")&&ge.endsWith(" ")){be.before=de.slice(0,de.length-1);ve.spaces.before=ge.slice(0,ge.length-1)}else if(de.startsWith(" ")&&ge.startsWith(" ")){be.after=de.slice(1);ve.spaces.after=ge.slice(1)}else{ve.value=ge}ae=new pe["default"]({value:" ",source:getTokenSourceSpan(ie,this.tokens[this.position-1]),sourceIndex:ie[he.FIELDS.START_POS],spaces:be,raws:ve})}if(this.currToken&&this.currToken[he.FIELDS.TYPE]===me.space){ae.spaces.after=this.optionalSpace(this.content());this.position++}return this.newNode(ae)};_.comma=function comma(){if(this.position===this.tokens.length-1){this.root.trailingComma=true;this.position++;return}this.current._inferEndPosition();var _=new re["default"]({source:{start:tokenStart(this.tokens[this.position+1])}});this.current.parent.append(_);this.current=_;this.position++};_.comment=function comment(){var _=this.currToken;this.newNode(new ne["default"]({value:this.content(),source:getTokenSource(_),sourceIndex:_[he.FIELDS.START_POS]}));this.position++};_.error=function error(_,X){throw this.root.error(_,X)};_.missingBackslash=function missingBackslash(){return this.error("Expected a backslash preceding the semicolon.",{index:this.currToken[he.FIELDS.START_POS]})};_.missingParenthesis=function missingParenthesis(){return this.expected("opening parenthesis",this.currToken[he.FIELDS.START_POS])};_.missingSquareBracket=function missingSquareBracket(){return this.expected("opening square bracket",this.currToken[he.FIELDS.START_POS])};_.unexpected=function unexpected(){return this.error("Unexpected '"+this.content()+"'. Escaping special characters with \\ may help.",this.currToken[he.FIELDS.START_POS])};_.namespace=function namespace(){var _=this.prevToken&&this.content(this.prevToken)||true;if(this.nextToken[he.FIELDS.TYPE]===me.word){this.position++;return this.word(_)}else if(this.nextToken[he.FIELDS.TYPE]===me.asterisk){this.position++;return this.universal(_)}};_.nesting=function nesting(){if(this.nextToken){var _=this.content(this.nextToken);if(_==="|"){this.position++;return}}var X=this.currToken;this.newNode(new fe["default"]({value:this.content(),source:getTokenSource(X),sourceIndex:X[he.FIELDS.START_POS]}));this.position++};_.parentheses=function parentheses(){var _=this.current.last;var X=1;this.position++;if(_&&_.type===ge.PSEUDO){var ee=new re["default"]({source:{start:tokenStart(this.tokens[this.position-1])}});var te=this.current;_.append(ee);this.current=ee;while(this.position<this.tokens.length&&X){if(this.currToken[he.FIELDS.TYPE]===me.openParenthesis){X++}if(this.currToken[he.FIELDS.TYPE]===me.closeParenthesis){X--}if(X){this.parse()}else{this.current.source.end=tokenEnd(this.currToken);this.current.parent.source.end=tokenEnd(this.currToken);this.position++}}this.current=te}else{var se=this.currToken;var ne="(";var ie;while(this.position<this.tokens.length&&X){if(this.currToken[he.FIELDS.TYPE]===me.openParenthesis){X++}if(this.currToken[he.FIELDS.TYPE]===me.closeParenthesis){X--}ie=this.currToken;ne+=this.parseParenthesisToken(this.currToken);this.position++}if(_){_.appendToPropertyAndEscape("value",ne,ne)}else{this.newNode(new ae["default"]({value:ne,source:getSource(se[he.FIELDS.START_LINE],se[he.FIELDS.START_COL],ie[he.FIELDS.END_LINE],ie[he.FIELDS.END_COL]),sourceIndex:se[he.FIELDS.START_POS]}))}}if(X){return this.expected("closing parenthesis",this.currToken[he.FIELDS.START_POS])}};_.pseudo=function pseudo(){var _=this;var X="";var ee=this.currToken;while(this.currToken&&this.currToken[he.FIELDS.TYPE]===me.colon){X+=this.content();this.position++}if(!this.currToken){return this.expected(["pseudo-class","pseudo-element"],this.position-1)}if(this.currToken[he.FIELDS.TYPE]===me.word){this.splitWord(false,(function(te,re){X+=te;_.newNode(new le["default"]({value:X,source:getTokenSourceSpan(ee,_.currToken),sourceIndex:ee[he.FIELDS.START_POS]}));if(re>1&&_.nextToken&&_.nextToken[he.FIELDS.TYPE]===me.openParenthesis){_.error("Misplaced parenthesis.",{index:_.nextToken[he.FIELDS.START_POS]})}}))}else{return this.expected(["pseudo-class","pseudo-element"],this.currToken[he.FIELDS.START_POS])}};_.space=function space(){var _=this.content();if(this.position===0||this.prevToken[he.FIELDS.TYPE]===me.comma||this.prevToken[he.FIELDS.TYPE]===me.openParenthesis||this.current.nodes.every((function(_){return _.type==="comment"}))){this.spaces=this.optionalSpace(_);this.position++}else if(this.position===this.tokens.length-1||this.nextToken[he.FIELDS.TYPE]===me.comma||this.nextToken[he.FIELDS.TYPE]===me.closeParenthesis){this.current.last.spaces.after=this.optionalSpace(_);this.position++}else{this.combinator()}};_.string=function string(){var _=this.currToken;this.newNode(new ae["default"]({value:this.content(),source:getTokenSource(_),sourceIndex:_[he.FIELDS.START_POS]}));this.position++};_.universal=function universal(_){var X=this.nextToken;if(X&&this.content(X)==="|"){this.position++;return this.namespace()}var ee=this.currToken;this.newNode(new ce["default"]({value:this.content(),source:getTokenSource(ee),sourceIndex:ee[he.FIELDS.START_POS]}),_);this.position++};_.splitWord=function splitWord(_,X){var ee=this;var te=this.nextToken;var re=this.content();while(te&&~[me.dollar,me.caret,me.equals,me.word].indexOf(te[he.FIELDS.TYPE])){this.position++;var ne=this.content();re+=ne;if(ne.lastIndexOf("\\")===ne.length-1){var ae=this.nextToken;if(ae&&ae[he.FIELDS.TYPE]===me.space){re+=this.requiredSpace(this.content(ae));this.position++}}te=this.nextToken}var le=indexesOf(re,".").filter((function(_){var X=re[_-1]==="\\";var ee=/^\d+\.\d+%$/.test(re);return!X&&!ee}));var ue=indexesOf(re,"#").filter((function(_){return re[_-1]!=="\\"}));var ce=indexesOf(re,"#{");if(ce.length){ue=ue.filter((function(_){return!~ce.indexOf(_)}))}var pe=(0,de["default"])(uniqs([0].concat(le,ue)));pe.forEach((function(te,ne){var ae=pe[ne+1]||re.length;var ce=re.slice(te,ae);if(ne===0&&X){return X.call(ee,ce,pe.length)}var fe;var de=ee.currToken;var me=de[he.FIELDS.START_POS]+pe[ne];var ge=getSource(de[1],de[2]+te,de[3],de[2]+(ae-1));if(~le.indexOf(te)){var be={value:ce.slice(1),source:ge,sourceIndex:me};fe=new se["default"](unescapeProp(be,"value"))}else if(~ue.indexOf(te)){var ve={value:ce.slice(1),source:ge,sourceIndex:me};fe=new ie["default"](unescapeProp(ve,"value"))}else{var ye={value:ce,source:ge,sourceIndex:me};unescapeProp(ye,"value");fe=new oe["default"](ye)}ee.newNode(fe,_);_=null}));this.position++};_.word=function word(_){var X=this.nextToken;if(X&&this.content(X)==="|"){this.position++;return this.namespace()}return this.splitWord(_)};_.loop=function loop(){while(this.position<this.tokens.length){this.parse(true)}this.current._inferEndPosition();return this.root};_.parse=function parse(_){switch(this.currToken[he.FIELDS.TYPE]){case me.space:this.space();break;case me.comment:this.comment();break;case me.openParenthesis:this.parentheses();break;case me.closeParenthesis:if(_){this.missingParenthesis()}break;case me.openSquare:this.attribute();break;case me.dollar:case me.caret:case me.equals:case me.word:this.word();break;case me.colon:this.pseudo();break;case me.comma:this.comma();break;case me.asterisk:this.universal();break;case me.ampersand:this.nesting();break;case me.slash:case me.combinator:this.combinator();break;case me.str:this.string();break;case me.closeSquare:this.missingSquareBracket();case me.semicolon:this.missingBackslash();default:this.unexpected()}};_.expected=function expected(_,X,ee){if(Array.isArray(_)){var te=_.pop();_=_.join(", ")+" or "+te}var re=/^[aeiou]/.test(_[0])?"an":"a";if(!ee){return this.error("Expected "+re+" "+_+".",{index:X})}return this.error("Expected "+re+" "+_+', found "'+ee+'" instead.',{index:X})};_.requiredSpace=function requiredSpace(_){return this.options.lossy?" ":_};_.optionalSpace=function optionalSpace(_){return this.options.lossy?"":_};_.lossySpace=function lossySpace(_,X){if(this.options.lossy){return X?" ":""}else{return _}};_.parseParenthesisToken=function parseParenthesisToken(_){var X=this.content(_);if(_[he.FIELDS.TYPE]===me.space){return this.requiredSpace(X)}else{return X}};_.newNode=function newNode(_,X){if(X){if(/^ +$/.test(X)){if(!this.options.lossy){this.spaces=(this.spaces||"")+X}X=true}_.namespace=X;unescapeProp(_,"namespace")}if(this.spaces){_.spaces.before=this.spaces;this.spaces=""}return this.current.append(_)};_.content=function content(_){if(_===void 0){_=this.currToken}return this.css.slice(_[he.FIELDS.START_POS],_[he.FIELDS.END_POS])};_.locateNextMeaningfulToken=function locateNextMeaningfulToken(_){if(_===void 0){_=this.position+1}var X=_;while(X<this.tokens.length){if(xe[this.tokens[X][he.FIELDS.TYPE]]){X++;continue}else{return X}}return-1};_createClass(Parser,[{key:"currToken",get:function get(){return this.tokens[this.position]}},{key:"nextToken",get:function get(){return this.tokens[this.position+1]}},{key:"prevToken",get:function get(){return this.tokens[this.position-1]}}]);return Parser}();X["default"]=ke;_.exports=X.default},6528:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6305));function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}var re=function(){function Processor(_,X){this.func=_||function noop(){};this.funcRes=null;this.options=X}var _=Processor.prototype;_._shouldUpdateSelector=function _shouldUpdateSelector(_,X){if(X===void 0){X={}}var ee=Object.assign({},this.options,X);if(ee.updateSelector===false){return false}else{return typeof _!=="string"}};_._isLossy=function _isLossy(_){if(_===void 0){_={}}var X=Object.assign({},this.options,_);if(X.lossless===false){return true}else{return false}};_._root=function _root(_,X){if(X===void 0){X={}}var ee=new te["default"](_,this._parseOptions(X));return ee.root};_._parseOptions=function _parseOptions(_){return{lossy:this._isLossy(_)}};_._run=function _run(_,X){var ee=this;if(X===void 0){X={}}return new Promise((function(te,re){try{var se=ee._root(_,X);Promise.resolve(ee.func(se)).then((function(te){var re=undefined;if(ee._shouldUpdateSelector(_,X)){re=se.toString();_.selector=re}return{transform:te,root:se,string:re}})).then(te,re)}catch(_){re(_);return}}))};_._runSync=function _runSync(_,X){if(X===void 0){X={}}var ee=this._root(_,X);var te=this.func(ee);if(te&&typeof te.then==="function"){throw new Error("Selector processor returned a promise to a synchronous call.")}var re=undefined;if(X.updateSelector&&typeof _!=="string"){re=ee.toString();_.selector=re}return{transform:te,root:ee,string:re}};_.ast=function ast(_,X){return this._run(_,X).then((function(_){return _.root}))};_.astSync=function astSync(_,X){return this._runSync(_,X).root};_.transform=function transform(_,X){return this._run(_,X).then((function(_){return _.transform}))};_.transformSync=function transformSync(_,X){return this._runSync(_,X).transform};_.process=function process(_,X){return this._run(_,X).then((function(_){return _.string||_.root.toString()}))};_.processSync=function processSync(_,X){var ee=this._runSync(_,X);return ee.string||ee.root.toString()};return Processor}();X["default"]=re;_.exports=X.default},9248:(_,X,ee)=>{"use strict";X.__esModule=true;X.unescapeValue=unescapeValue;X["default"]=void 0;var te=_interopRequireDefault(ee(441));var re=_interopRequireDefault(ee(3590));var se=_interopRequireDefault(ee(999));var ne=ee(8600);var ie;function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _defineProperties(_,X){for(var ee=0;ee<X.length;ee++){var te=X[ee];te.enumerable=te.enumerable||false;te.configurable=true;if("value"in te)te.writable=true;Object.defineProperty(_,te.key,te)}}function _createClass(_,X,ee){if(X)_defineProperties(_.prototype,X);if(ee)_defineProperties(_,ee);return _}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var oe=ee(6124);var ae=/^('|")([^]*)\1$/;var le=oe((function(){}),"Assigning an attribute a value containing characters that might need to be escaped is deprecated. "+"Call attribute.setValue() instead.");var ue=oe((function(){}),"Assigning attr.quoted is deprecated and has no effect. Assign to attr.quoteMark instead.");var ce=oe((function(){}),"Constructing an Attribute selector with a value without specifying quoteMark is deprecated. Note: The value should be unescaped now.");function unescapeValue(_){var X=false;var ee=null;var te=_;var se=te.match(ae);if(se){ee=se[1];te=se[2]}te=(0,re["default"])(te);if(te!==_){X=true}return{deprecatedUsage:X,unescaped:te,quoteMark:ee}}function handleDeprecatedContructorOpts(_){if(_.quoteMark!==undefined){return _}if(_.value===undefined){return _}ce();var X=unescapeValue(_.value),ee=X.quoteMark,te=X.unescaped;if(!_.raws){_.raws={}}if(_.raws.value===undefined){_.raws.value=_.value}_.value=te;_.quoteMark=ee;return _}var pe=function(_){_inheritsLoose(Attribute,_);function Attribute(X){var ee;if(X===void 0){X={}}ee=_.call(this,handleDeprecatedContructorOpts(X))||this;ee.type=ne.ATTRIBUTE;ee.raws=ee.raws||{};Object.defineProperty(ee.raws,"unquoted",{get:oe((function(){return ee.value}),"attr.raws.unquoted is deprecated. Call attr.value instead."),set:oe((function(){return ee.value}),"Setting attr.raws.unquoted is deprecated and has no effect. attr.value is unescaped by default now.")});ee._constructed=true;return ee}var X=Attribute.prototype;X.getQuotedValue=function getQuotedValue(_){if(_===void 0){_={}}var X=this._determineQuoteMark(_);var ee=fe[X];var re=(0,te["default"])(this._value,ee);return re};X._determineQuoteMark=function _determineQuoteMark(_){return _.smart?this.smartQuoteMark(_):this.preferredQuoteMark(_)};X.setValue=function setValue(_,X){if(X===void 0){X={}}this._value=_;this._quoteMark=this._determineQuoteMark(X);this._syncRawValue()};X.smartQuoteMark=function smartQuoteMark(_){var X=this.value;var ee=X.replace(/[^']/g,"").length;var re=X.replace(/[^"]/g,"").length;if(ee+re===0){var se=(0,te["default"])(X,{isIdentifier:true});if(se===X){return Attribute.NO_QUOTE}else{var ne=this.preferredQuoteMark(_);if(ne===Attribute.NO_QUOTE){var ie=this.quoteMark||_.quoteMark||Attribute.DOUBLE_QUOTE;var oe=fe[ie];var ae=(0,te["default"])(X,oe);if(ae.length<se.length){return ie}}return ne}}else if(re===ee){return this.preferredQuoteMark(_)}else if(re<ee){return Attribute.DOUBLE_QUOTE}else{return Attribute.SINGLE_QUOTE}};X.preferredQuoteMark=function preferredQuoteMark(_){var X=_.preferCurrentQuoteMark?this.quoteMark:_.quoteMark;if(X===undefined){X=_.preferCurrentQuoteMark?_.quoteMark:this.quoteMark}if(X===undefined){X=Attribute.DOUBLE_QUOTE}return X};X._syncRawValue=function _syncRawValue(){var _=(0,te["default"])(this._value,fe[this.quoteMark]);if(_===this._value){if(this.raws){delete this.raws.value}}else{this.raws.value=_}};X._handleEscapes=function _handleEscapes(_,X){if(this._constructed){var ee=(0,te["default"])(X,{isIdentifier:true});if(ee!==X){this.raws[_]=ee}else{delete this.raws[_]}}};X._spacesFor=function _spacesFor(_){var X={before:"",after:""};var ee=this.spaces[_]||{};var te=this.raws.spaces&&this.raws.spaces[_]||{};return Object.assign(X,ee,te)};X._stringFor=function _stringFor(_,X,ee){if(X===void 0){X=_}if(ee===void 0){ee=defaultAttrConcat}var te=this._spacesFor(X);return ee(this.stringifyProperty(_),te)};X.offsetOf=function offsetOf(_){var X=1;var ee=this._spacesFor("attribute");X+=ee.before.length;if(_==="namespace"||_==="ns"){return this.namespace?X:-1}if(_==="attributeNS"){return X}X+=this.namespaceString.length;if(this.namespace){X+=1}if(_==="attribute"){return X}X+=this.stringifyProperty("attribute").length;X+=ee.after.length;var te=this._spacesFor("operator");X+=te.before.length;var re=this.stringifyProperty("operator");if(_==="operator"){return re?X:-1}X+=re.length;X+=te.after.length;var se=this._spacesFor("value");X+=se.before.length;var ne=this.stringifyProperty("value");if(_==="value"){return ne?X:-1}X+=ne.length;X+=se.after.length;var ie=this._spacesFor("insensitive");X+=ie.before.length;if(_==="insensitive"){return this.insensitive?X:-1}return-1};X.toString=function toString(){var _=this;var X=[this.rawSpaceBefore,"["];X.push(this._stringFor("qualifiedAttribute","attribute"));if(this.operator&&(this.value||this.value==="")){X.push(this._stringFor("operator"));X.push(this._stringFor("value"));X.push(this._stringFor("insensitiveFlag","insensitive",(function(X,ee){if(X.length>0&&!_.quoted&&ee.before.length===0&&!(_.spaces.value&&_.spaces.value.after)){ee.before=" "}return defaultAttrConcat(X,ee)})))}X.push("]");X.push(this.rawSpaceAfter);return X.join("")};_createClass(Attribute,[{key:"quoted",get:function get(){var _=this.quoteMark;return _==="'"||_==='"'},set:function set(_){ue()}},{key:"quoteMark",get:function get(){return this._quoteMark},set:function set(_){if(!this._constructed){this._quoteMark=_;return}if(this._quoteMark!==_){this._quoteMark=_;this._syncRawValue()}}},{key:"qualifiedAttribute",get:function get(){return this.qualifiedName(this.raws.attribute||this.attribute)}},{key:"insensitiveFlag",get:function get(){return this.insensitive?"i":""}},{key:"value",get:function get(){return this._value},set:function set(_){if(this._constructed){var X=unescapeValue(_),ee=X.deprecatedUsage,te=X.unescaped,re=X.quoteMark;if(ee){le()}if(te===this._value&&re===this._quoteMark){return}this._value=te;this._quoteMark=re;this._syncRawValue()}else{this._value=_}}},{key:"insensitive",get:function get(){return this._insensitive},set:function set(_){if(!_){this._insensitive=false;if(this.raws&&(this.raws.insensitiveFlag==="I"||this.raws.insensitiveFlag==="i")){this.raws.insensitiveFlag=undefined}}this._insensitive=_}},{key:"attribute",get:function get(){return this._attribute},set:function set(_){this._handleEscapes("attribute",_);this._attribute=_}}]);return Attribute}(se["default"]);X["default"]=pe;pe.NO_QUOTE=null;pe.SINGLE_QUOTE="'";pe.DOUBLE_QUOTE='"';var fe=(ie={"'":{quotes:"single",wrap:true},'"':{quotes:"double",wrap:true}},ie[null]={isIdentifier:true},ie);function defaultAttrConcat(_,X){return""+X.before+_+X.after}},6870:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(441));var re=ee(4513);var se=_interopRequireDefault(ee(6373));var ne=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _defineProperties(_,X){for(var ee=0;ee<X.length;ee++){var te=X[ee];te.enumerable=te.enumerable||false;te.configurable=true;if("value"in te)te.writable=true;Object.defineProperty(_,te.key,te)}}function _createClass(_,X,ee){if(X)_defineProperties(_.prototype,X);if(ee)_defineProperties(_,ee);return _}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var ie=function(_){_inheritsLoose(ClassName,_);function ClassName(X){var ee;ee=_.call(this,X)||this;ee.type=ne.CLASS;ee._constructed=true;return ee}var X=ClassName.prototype;X.valueToString=function valueToString(){return"."+_.prototype.valueToString.call(this)};_createClass(ClassName,[{key:"value",get:function get(){return this._value},set:function set(_){if(this._constructed){var X=(0,te["default"])(_,{isIdentifier:true});if(X!==_){(0,re.ensureObject)(this,"raws");this.raws.value=X}else if(this.raws){delete this.raws.value}}this._value=_}}]);return ClassName}(se["default"]);X["default"]=ie;_.exports=X.default},2537:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6373));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Combinator,_);function Combinator(X){var ee;ee=_.call(this,X)||this;ee.type=re.COMBINATOR;return ee}return Combinator}(te["default"]);X["default"]=se;_.exports=X.default},5047:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6373));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Comment,_);function Comment(X){var ee;ee=_.call(this,X)||this;ee.type=re.COMMENT;return ee}return Comment}(te["default"]);X["default"]=se;_.exports=X.default},6734:(_,X,ee)=>{"use strict";X.__esModule=true;X.universal=X.tag=X.string=X.selector=X.root=X.pseudo=X.nesting=X.id=X.comment=X.combinator=X.className=X.attribute=void 0;var te=_interopRequireDefault(ee(9248));var re=_interopRequireDefault(ee(6870));var se=_interopRequireDefault(ee(2537));var ne=_interopRequireDefault(ee(5047));var ie=_interopRequireDefault(ee(8393));var oe=_interopRequireDefault(ee(6060));var ae=_interopRequireDefault(ee(5326));var le=_interopRequireDefault(ee(422));var ue=_interopRequireDefault(ee(5013));var ce=_interopRequireDefault(ee(435));var pe=_interopRequireDefault(ee(9443));var fe=_interopRequireDefault(ee(1165));function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}var de=function attribute(_){return new te["default"](_)};X.attribute=de;var he=function className(_){return new re["default"](_)};X.className=he;var me=function combinator(_){return new se["default"](_)};X.combinator=me;var ge=function comment(_){return new ne["default"](_)};X.comment=ge;var be=function id(_){return new ie["default"](_)};X.id=be;var ve=function nesting(_){return new oe["default"](_)};X.nesting=ve;var ye=function pseudo(_){return new ae["default"](_)};X.pseudo=ye;var we=function root(_){return new le["default"](_)};X.root=we;var xe=function selector(_){return new ue["default"](_)};X.selector=xe;var ke=function string(_){return new ce["default"](_)};X.string=ke;var Se=function tag(_){return new pe["default"](_)};X.tag=Se;var _e=function universal(_){return new fe["default"](_)};X.universal=_e},7675:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6373));var re=_interopRequireWildcard(ee(8600));function _getRequireWildcardCache(){if(typeof WeakMap!=="function")return null;var _=new WeakMap;_getRequireWildcardCache=function _getRequireWildcardCache(){return _};return _}function _interopRequireWildcard(_){if(_&&_.__esModule){return _}if(_===null||typeof _!=="object"&&typeof _!=="function"){return{default:_}}var X=_getRequireWildcardCache();if(X&&X.has(_)){return X.get(_)}var ee={};var te=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var re in _){if(Object.prototype.hasOwnProperty.call(_,re)){var se=te?Object.getOwnPropertyDescriptor(_,re):null;if(se&&(se.get||se.set)){Object.defineProperty(ee,re,se)}else{ee[re]=_[re]}}}ee["default"]=_;if(X){X.set(_,ee)}return ee}function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _createForOfIteratorHelperLoose(_,X){var ee;if(typeof Symbol==="undefined"||_[Symbol.iterator]==null){if(Array.isArray(_)||(ee=_unsupportedIterableToArray(_))||X&&_&&typeof _.length==="number"){if(ee)_=ee;var te=0;return function(){if(te>=_.length)return{done:true};return{done:false,value:_[te++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}ee=_[Symbol.iterator]();return ee.next.bind(ee)}function _unsupportedIterableToArray(_,X){if(!_)return;if(typeof _==="string")return _arrayLikeToArray(_,X);var ee=Object.prototype.toString.call(_).slice(8,-1);if(ee==="Object"&&_.constructor)ee=_.constructor.name;if(ee==="Map"||ee==="Set")return Array.from(_);if(ee==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(ee))return _arrayLikeToArray(_,X)}function _arrayLikeToArray(_,X){if(X==null||X>_.length)X=_.length;for(var ee=0,te=new Array(X);ee<X;ee++){te[ee]=_[ee]}return te}function _defineProperties(_,X){for(var ee=0;ee<X.length;ee++){var te=X[ee];te.enumerable=te.enumerable||false;te.configurable=true;if("value"in te)te.writable=true;Object.defineProperty(_,te.key,te)}}function _createClass(_,X,ee){if(X)_defineProperties(_.prototype,X);if(ee)_defineProperties(_,ee);return _}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Container,_);function Container(X){var ee;ee=_.call(this,X)||this;if(!ee.nodes){ee.nodes=[]}return ee}var X=Container.prototype;X.append=function append(_){_.parent=this;this.nodes.push(_);return this};X.prepend=function prepend(_){_.parent=this;this.nodes.unshift(_);return this};X.at=function at(_){return this.nodes[_]};X.index=function index(_){if(typeof _==="number"){return _}return this.nodes.indexOf(_)};X.removeChild=function removeChild(_){_=this.index(_);this.at(_).parent=undefined;this.nodes.splice(_,1);var X;for(var ee in this.indexes){X=this.indexes[ee];if(X>=_){this.indexes[ee]=X-1}}return this};X.removeAll=function removeAll(){for(var _=_createForOfIteratorHelperLoose(this.nodes),X;!(X=_()).done;){var ee=X.value;ee.parent=undefined}this.nodes=[];return this};X.empty=function empty(){return this.removeAll()};X.insertAfter=function insertAfter(_,X){X.parent=this;var ee=this.index(_);this.nodes.splice(ee+1,0,X);X.parent=this;var te;for(var re in this.indexes){te=this.indexes[re];if(ee<=te){this.indexes[re]=te+1}}return this};X.insertBefore=function insertBefore(_,X){X.parent=this;var ee=this.index(_);this.nodes.splice(ee,0,X);X.parent=this;var te;for(var re in this.indexes){te=this.indexes[re];if(te<=ee){this.indexes[re]=te+1}}return this};X._findChildAtPosition=function _findChildAtPosition(_,X){var ee=undefined;this.each((function(te){if(te.atPosition){var re=te.atPosition(_,X);if(re){ee=re;return false}}else if(te.isAtPosition(_,X)){ee=te;return false}}));return ee};X.atPosition=function atPosition(_,X){if(this.isAtPosition(_,X)){return this._findChildAtPosition(_,X)||this}else{return undefined}};X._inferEndPosition=function _inferEndPosition(){if(this.last&&this.last.source&&this.last.source.end){this.source=this.source||{};this.source.end=this.source.end||{};Object.assign(this.source.end,this.last.source.end)}};X.each=function each(_){if(!this.lastEach){this.lastEach=0}if(!this.indexes){this.indexes={}}this.lastEach++;var X=this.lastEach;this.indexes[X]=0;if(!this.length){return undefined}var ee,te;while(this.indexes[X]<this.length){ee=this.indexes[X];te=_(this.at(ee),ee);if(te===false){break}this.indexes[X]+=1}delete this.indexes[X];if(te===false){return false}};X.walk=function walk(_){return this.each((function(X,ee){var te=_(X,ee);if(te!==false&&X.length){te=X.walk(_)}if(te===false){return false}}))};X.walkAttributes=function walkAttributes(_){var X=this;return this.walk((function(ee){if(ee.type===re.ATTRIBUTE){return _.call(X,ee)}}))};X.walkClasses=function walkClasses(_){var X=this;return this.walk((function(ee){if(ee.type===re.CLASS){return _.call(X,ee)}}))};X.walkCombinators=function walkCombinators(_){var X=this;return this.walk((function(ee){if(ee.type===re.COMBINATOR){return _.call(X,ee)}}))};X.walkComments=function walkComments(_){var X=this;return this.walk((function(ee){if(ee.type===re.COMMENT){return _.call(X,ee)}}))};X.walkIds=function walkIds(_){var X=this;return this.walk((function(ee){if(ee.type===re.ID){return _.call(X,ee)}}))};X.walkNesting=function walkNesting(_){var X=this;return this.walk((function(ee){if(ee.type===re.NESTING){return _.call(X,ee)}}))};X.walkPseudos=function walkPseudos(_){var X=this;return this.walk((function(ee){if(ee.type===re.PSEUDO){return _.call(X,ee)}}))};X.walkTags=function walkTags(_){var X=this;return this.walk((function(ee){if(ee.type===re.TAG){return _.call(X,ee)}}))};X.walkUniversals=function walkUniversals(_){var X=this;return this.walk((function(ee){if(ee.type===re.UNIVERSAL){return _.call(X,ee)}}))};X.split=function split(_){var X=this;var ee=[];return this.reduce((function(te,re,se){var ne=_.call(X,re);ee.push(re);if(ne){te.push(ee);ee=[]}else if(se===X.length-1){te.push(ee)}return te}),[])};X.map=function map(_){return this.nodes.map(_)};X.reduce=function reduce(_,X){return this.nodes.reduce(_,X)};X.every=function every(_){return this.nodes.every(_)};X.some=function some(_){return this.nodes.some(_)};X.filter=function filter(_){return this.nodes.filter(_)};X.sort=function sort(_){return this.nodes.sort(_)};X.toString=function toString(){return this.map(String).join("")};_createClass(Container,[{key:"first",get:function get(){return this.at(0)}},{key:"last",get:function get(){return this.at(this.length-1)}},{key:"length",get:function get(){return this.nodes.length}}]);return Container}(te["default"]);X["default"]=se;_.exports=X.default},1493:(_,X,ee)=>{"use strict";X.__esModule=true;X.isNode=isNode;X.isPseudoElement=isPseudoElement;X.isPseudoClass=isPseudoClass;X.isContainer=isContainer;X.isNamespace=isNamespace;X.isUniversal=X.isTag=X.isString=X.isSelector=X.isRoot=X.isPseudo=X.isNesting=X.isIdentifier=X.isComment=X.isCombinator=X.isClassName=X.isAttribute=void 0;var te=ee(8600);var re;var se=(re={},re[te.ATTRIBUTE]=true,re[te.CLASS]=true,re[te.COMBINATOR]=true,re[te.COMMENT]=true,re[te.ID]=true,re[te.NESTING]=true,re[te.PSEUDO]=true,re[te.ROOT]=true,re[te.SELECTOR]=true,re[te.STRING]=true,re[te.TAG]=true,re[te.UNIVERSAL]=true,re);function isNode(_){return typeof _==="object"&&se[_.type]}function isNodeType(_,X){return isNode(X)&&X.type===_}var ne=isNodeType.bind(null,te.ATTRIBUTE);X.isAttribute=ne;var ie=isNodeType.bind(null,te.CLASS);X.isClassName=ie;var oe=isNodeType.bind(null,te.COMBINATOR);X.isCombinator=oe;var ae=isNodeType.bind(null,te.COMMENT);X.isComment=ae;var le=isNodeType.bind(null,te.ID);X.isIdentifier=le;var ue=isNodeType.bind(null,te.NESTING);X.isNesting=ue;var ce=isNodeType.bind(null,te.PSEUDO);X.isPseudo=ce;var pe=isNodeType.bind(null,te.ROOT);X.isRoot=pe;var fe=isNodeType.bind(null,te.SELECTOR);X.isSelector=fe;var de=isNodeType.bind(null,te.STRING);X.isString=de;var he=isNodeType.bind(null,te.TAG);X.isTag=he;var me=isNodeType.bind(null,te.UNIVERSAL);X.isUniversal=me;function isPseudoElement(_){return ce(_)&&_.value&&(_.value.startsWith("::")||_.value.toLowerCase()===":before"||_.value.toLowerCase()===":after"||_.value.toLowerCase()===":first-letter"||_.value.toLowerCase()===":first-line")}function isPseudoClass(_){return ce(_)&&!isPseudoElement(_)}function isContainer(_){return!!(isNode(_)&&_.walk)}function isNamespace(_){return ne(_)||he(_)}},8393:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6373));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(ID,_);function ID(X){var ee;ee=_.call(this,X)||this;ee.type=re.ID;return ee}var X=ID.prototype;X.valueToString=function valueToString(){return"#"+_.prototype.valueToString.call(this)};return ID}(te["default"]);X["default"]=se;_.exports=X.default},3110:(_,X,ee)=>{"use strict";X.__esModule=true;var te=ee(8600);Object.keys(te).forEach((function(_){if(_==="default"||_==="__esModule")return;if(_ in X&&X[_]===te[_])return;X[_]=te[_]}));var re=ee(6734);Object.keys(re).forEach((function(_){if(_==="default"||_==="__esModule")return;if(_ in X&&X[_]===re[_])return;X[_]=re[_]}));var se=ee(1493);Object.keys(se).forEach((function(_){if(_==="default"||_==="__esModule")return;if(_ in X&&X[_]===se[_])return;X[_]=se[_]}))},999:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(441));var re=ee(4513);var se=_interopRequireDefault(ee(6373));function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _defineProperties(_,X){for(var ee=0;ee<X.length;ee++){var te=X[ee];te.enumerable=te.enumerable||false;te.configurable=true;if("value"in te)te.writable=true;Object.defineProperty(_,te.key,te)}}function _createClass(_,X,ee){if(X)_defineProperties(_.prototype,X);if(ee)_defineProperties(_,ee);return _}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var ne=function(_){_inheritsLoose(Namespace,_);function Namespace(){return _.apply(this,arguments)||this}var X=Namespace.prototype;X.qualifiedName=function qualifiedName(_){if(this.namespace){return this.namespaceString+"|"+_}else{return _}};X.valueToString=function valueToString(){return this.qualifiedName(_.prototype.valueToString.call(this))};_createClass(Namespace,[{key:"namespace",get:function get(){return this._namespace},set:function set(_){if(_===true||_==="*"||_==="&"){this._namespace=_;if(this.raws){delete this.raws.namespace}return}var X=(0,te["default"])(_,{isIdentifier:true});this._namespace=_;if(X!==_){(0,re.ensureObject)(this,"raws");this.raws.namespace=X}else if(this.raws){delete this.raws.namespace}}},{key:"ns",get:function get(){return this._namespace},set:function set(_){this.namespace=_}},{key:"namespaceString",get:function get(){if(this.namespace){var _=this.stringifyProperty("namespace");if(_===true){return""}else{return _}}else{return""}}}]);return Namespace}(se["default"]);X["default"]=ne;_.exports=X.default},6060:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6373));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Nesting,_);function Nesting(X){var ee;ee=_.call(this,X)||this;ee.type=re.NESTING;ee.value="&";return ee}return Nesting}(te["default"]);X["default"]=se;_.exports=X.default},6373:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=ee(4513);function _defineProperties(_,X){for(var ee=0;ee<X.length;ee++){var te=X[ee];te.enumerable=te.enumerable||false;te.configurable=true;if("value"in te)te.writable=true;Object.defineProperty(_,te.key,te)}}function _createClass(_,X,ee){if(X)_defineProperties(_.prototype,X);if(ee)_defineProperties(_,ee);return _}var re=function cloneNode(_,X){if(typeof _!=="object"||_===null){return _}var ee=new _.constructor;for(var te in _){if(!_.hasOwnProperty(te)){continue}var re=_[te];var se=typeof re;if(te==="parent"&&se==="object"){if(X){ee[te]=X}}else if(re instanceof Array){ee[te]=re.map((function(_){return cloneNode(_,ee)}))}else{ee[te]=cloneNode(re,ee)}}return ee};var se=function(){function Node(_){if(_===void 0){_={}}Object.assign(this,_);this.spaces=this.spaces||{};this.spaces.before=this.spaces.before||"";this.spaces.after=this.spaces.after||""}var _=Node.prototype;_.remove=function remove(){if(this.parent){this.parent.removeChild(this)}this.parent=undefined;return this};_.replaceWith=function replaceWith(){if(this.parent){for(var _ in arguments){this.parent.insertBefore(this,arguments[_])}this.remove()}return this};_.next=function next(){return this.parent.at(this.parent.index(this)+1)};_.prev=function prev(){return this.parent.at(this.parent.index(this)-1)};_.clone=function clone(_){if(_===void 0){_={}}var X=re(this);for(var ee in _){X[ee]=_[ee]}return X};_.appendToPropertyAndEscape=function appendToPropertyAndEscape(_,X,ee){if(!this.raws){this.raws={}}var te=this[_];var re=this.raws[_];this[_]=te+X;if(re||ee!==X){this.raws[_]=(re||te)+ee}else{delete this.raws[_]}};_.setPropertyAndEscape=function setPropertyAndEscape(_,X,ee){if(!this.raws){this.raws={}}this[_]=X;this.raws[_]=ee};_.setPropertyWithoutEscape=function setPropertyWithoutEscape(_,X){this[_]=X;if(this.raws){delete this.raws[_]}};_.isAtPosition=function isAtPosition(_,X){if(this.source&&this.source.start&&this.source.end){if(this.source.start.line>_){return false}if(this.source.end.line<_){return false}if(this.source.start.line===_&&this.source.start.column>X){return false}if(this.source.end.line===_&&this.source.end.column<X){return false}return true}return undefined};_.stringifyProperty=function stringifyProperty(_){return this.raws&&this.raws[_]||this[_]};_.valueToString=function valueToString(){return String(this.stringifyProperty("value"))};_.toString=function toString(){return[this.rawSpaceBefore,this.valueToString(),this.rawSpaceAfter].join("")};_createClass(Node,[{key:"rawSpaceBefore",get:function get(){var _=this.raws&&this.raws.spaces&&this.raws.spaces.before;if(_===undefined){_=this.spaces&&this.spaces.before}return _||""},set:function set(_){(0,te.ensureObject)(this,"raws","spaces");this.raws.spaces.before=_}},{key:"rawSpaceAfter",get:function get(){var _=this.raws&&this.raws.spaces&&this.raws.spaces.after;if(_===undefined){_=this.spaces.after}return _||""},set:function set(_){(0,te.ensureObject)(this,"raws","spaces");this.raws.spaces.after=_}}]);return Node}();X["default"]=se;_.exports=X.default},5326:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(7675));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Pseudo,_);function Pseudo(X){var ee;ee=_.call(this,X)||this;ee.type=re.PSEUDO;return ee}var X=Pseudo.prototype;X.toString=function toString(){var _=this.length?"("+this.map(String).join(",")+")":"";return[this.rawSpaceBefore,this.stringifyProperty("value"),_,this.rawSpaceAfter].join("")};return Pseudo}(te["default"]);X["default"]=se;_.exports=X.default},422:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(7675));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _defineProperties(_,X){for(var ee=0;ee<X.length;ee++){var te=X[ee];te.enumerable=te.enumerable||false;te.configurable=true;if("value"in te)te.writable=true;Object.defineProperty(_,te.key,te)}}function _createClass(_,X,ee){if(X)_defineProperties(_.prototype,X);if(ee)_defineProperties(_,ee);return _}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Root,_);function Root(X){var ee;ee=_.call(this,X)||this;ee.type=re.ROOT;return ee}var X=Root.prototype;X.toString=function toString(){var _=this.reduce((function(_,X){_.push(String(X));return _}),[]).join(",");return this.trailingComma?_+",":_};X.error=function error(_,X){if(this._error){return this._error(_,X)}else{return new Error(_)}};_createClass(Root,[{key:"errorGenerator",set:function set(_){this._error=_}}]);return Root}(te["default"]);X["default"]=se;_.exports=X.default},5013:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(7675));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Selector,_);function Selector(X){var ee;ee=_.call(this,X)||this;ee.type=re.SELECTOR;return ee}return Selector}(te["default"]);X["default"]=se;_.exports=X.default},435:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(6373));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(String,_);function String(X){var ee;ee=_.call(this,X)||this;ee.type=re.STRING;return ee}return String}(te["default"]);X["default"]=se;_.exports=X.default},9443:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(999));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Tag,_);function Tag(X){var ee;ee=_.call(this,X)||this;ee.type=re.TAG;return ee}return Tag}(te["default"]);X["default"]=se;_.exports=X.default},8600:(_,X)=>{"use strict";X.__esModule=true;X.UNIVERSAL=X.ATTRIBUTE=X.CLASS=X.COMBINATOR=X.COMMENT=X.ID=X.NESTING=X.PSEUDO=X.ROOT=X.SELECTOR=X.STRING=X.TAG=void 0;var ee="tag";X.TAG=ee;var te="string";X.STRING=te;var re="selector";X.SELECTOR=re;var se="root";X.ROOT=se;var ne="pseudo";X.PSEUDO=ne;var ie="nesting";X.NESTING=ie;var oe="id";X.ID=oe;var ae="comment";X.COMMENT=ae;var le="combinator";X.COMBINATOR=le;var ue="class";X.CLASS=ue;var ce="attribute";X.ATTRIBUTE=ce;var pe="universal";X.UNIVERSAL=pe},1165:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=void 0;var te=_interopRequireDefault(ee(999));var re=ee(8600);function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}function _inheritsLoose(_,X){_.prototype=Object.create(X.prototype);_.prototype.constructor=_;_setPrototypeOf(_,X)}function _setPrototypeOf(_,X){_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(_,X){_.__proto__=X;return _};return _setPrototypeOf(_,X)}var se=function(_){_inheritsLoose(Universal,_);function Universal(X){var ee;ee=_.call(this,X)||this;ee.type=re.UNIVERSAL;ee.value="*";return ee}return Universal}(te["default"]);X["default"]=se;_.exports=X.default},2173:(_,X)=>{"use strict";X.__esModule=true;X["default"]=sortAscending;function sortAscending(_){return _.sort((function(_,X){return _-X}))}_.exports=X.default},8553:(_,X)=>{"use strict";X.__esModule=true;X.combinator=X.word=X.comment=X.str=X.tab=X.newline=X.feed=X.cr=X.backslash=X.bang=X.slash=X.doubleQuote=X.singleQuote=X.space=X.greaterThan=X.pipe=X.equals=X.plus=X.caret=X.tilde=X.dollar=X.closeSquare=X.openSquare=X.closeParenthesis=X.openParenthesis=X.semicolon=X.colon=X.comma=X.at=X.asterisk=X.ampersand=void 0;var ee=38;X.ampersand=ee;var te=42;X.asterisk=te;var re=64;X.at=re;var se=44;X.comma=se;var ne=58;X.colon=ne;var ie=59;X.semicolon=ie;var oe=40;X.openParenthesis=oe;var ae=41;X.closeParenthesis=ae;var le=91;X.openSquare=le;var ue=93;X.closeSquare=ue;var ce=36;X.dollar=ce;var pe=126;X.tilde=pe;var fe=94;X.caret=fe;var de=43;X.plus=de;var he=61;X.equals=he;var me=124;X.pipe=me;var ge=62;X.greaterThan=ge;var be=32;X.space=be;var ve=39;X.singleQuote=ve;var ye=34;X.doubleQuote=ye;var we=47;X.slash=we;var xe=33;X.bang=xe;var ke=92;X.backslash=ke;var Se=13;X.cr=Se;var _e=12;X.feed=_e;var Pe=10;X.newline=Pe;var Oe=9;X.tab=Oe;var je=ve;X.str=je;var Te=-1;X.comment=Te;var Ee=-2;X.word=Ee;var Fe=-3;X.combinator=Fe},2133:(_,X,ee)=>{"use strict";X.__esModule=true;X["default"]=tokenize;X.FIELDS=void 0;var te=_interopRequireWildcard(ee(8553));var re,se;function _getRequireWildcardCache(){if(typeof WeakMap!=="function")return null;var _=new WeakMap;_getRequireWildcardCache=function _getRequireWildcardCache(){return _};return _}function _interopRequireWildcard(_){if(_&&_.__esModule){return _}if(_===null||typeof _!=="object"&&typeof _!=="function"){return{default:_}}var X=_getRequireWildcardCache();if(X&&X.has(_)){return X.get(_)}var ee={};var te=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var re in _){if(Object.prototype.hasOwnProperty.call(_,re)){var se=te?Object.getOwnPropertyDescriptor(_,re):null;if(se&&(se.get||se.set)){Object.defineProperty(ee,re,se)}else{ee[re]=_[re]}}}ee["default"]=_;if(X){X.set(_,ee)}return ee}var ne=(re={},re[te.tab]=true,re[te.newline]=true,re[te.cr]=true,re[te.feed]=true,re);var ie=(se={},se[te.space]=true,se[te.tab]=true,se[te.newline]=true,se[te.cr]=true,se[te.feed]=true,se[te.ampersand]=true,se[te.asterisk]=true,se[te.bang]=true,se[te.comma]=true,se[te.colon]=true,se[te.semicolon]=true,se[te.openParenthesis]=true,se[te.closeParenthesis]=true,se[te.openSquare]=true,se[te.closeSquare]=true,se[te.singleQuote]=true,se[te.doubleQuote]=true,se[te.plus]=true,se[te.pipe]=true,se[te.tilde]=true,se[te.greaterThan]=true,se[te.equals]=true,se[te.dollar]=true,se[te.caret]=true,se[te.slash]=true,se);var oe={};var ae="0123456789abcdefABCDEF";for(var le=0;le<ae.length;le++){oe[ae.charCodeAt(le)]=true}function consumeWord(_,X){var ee=X;var re;do{re=_.charCodeAt(ee);if(ie[re]){return ee-1}else if(re===te.backslash){ee=consumeEscape(_,ee)+1}else{ee++}}while(ee<_.length);return ee-1}function consumeEscape(_,X){var ee=X;var re=_.charCodeAt(ee+1);if(ne[re]){}else if(oe[re]){var se=0;do{ee++;se++;re=_.charCodeAt(ee+1)}while(oe[re]&&se<6);if(se<6&&re===te.space){ee++}}else{ee++}return ee}var ue={TYPE:0,START_LINE:1,START_COL:2,END_LINE:3,END_COL:4,START_POS:5,END_POS:6};X.FIELDS=ue;function tokenize(_){var X=[];var ee=_.css.valueOf();var re=ee,se=re.length;var ne=-1;var ie=1;var oe=0;var ae=0;var le,ue,ce,pe,fe,de,he,me,ge,be,ve,ye,we;function unclosed(X,te){if(_.safe){ee+=te;ge=ee.length-1}else{throw _.error("Unclosed "+X,ie,oe-ne,oe)}}while(oe<se){le=ee.charCodeAt(oe);if(le===te.newline){ne=oe;ie+=1}switch(le){case te.space:case te.tab:case te.newline:case te.cr:case te.feed:ge=oe;do{ge+=1;le=ee.charCodeAt(ge);if(le===te.newline){ne=ge;ie+=1}}while(le===te.space||le===te.newline||le===te.tab||le===te.cr||le===te.feed);we=te.space;pe=ie;ce=ge-ne-1;ae=ge;break;case te.plus:case te.greaterThan:case te.tilde:case te.pipe:ge=oe;do{ge+=1;le=ee.charCodeAt(ge)}while(le===te.plus||le===te.greaterThan||le===te.tilde||le===te.pipe);we=te.combinator;pe=ie;ce=oe-ne;ae=ge;break;case te.asterisk:case te.ampersand:case te.bang:case te.comma:case te.equals:case te.dollar:case te.caret:case te.openSquare:case te.closeSquare:case te.colon:case te.semicolon:case te.openParenthesis:case te.closeParenthesis:ge=oe;we=le;pe=ie;ce=oe-ne;ae=ge+1;break;case te.singleQuote:case te.doubleQuote:ye=le===te.singleQuote?"'":'"';ge=oe;do{fe=false;ge=ee.indexOf(ye,ge+1);if(ge===-1){unclosed("quote",ye)}de=ge;while(ee.charCodeAt(de-1)===te.backslash){de-=1;fe=!fe}}while(fe);we=te.str;pe=ie;ce=oe-ne;ae=ge+1;break;default:if(le===te.slash&&ee.charCodeAt(oe+1)===te.asterisk){ge=ee.indexOf("*/",oe+2)+1;if(ge===0){unclosed("comment","*/")}ue=ee.slice(oe,ge+1);me=ue.split("\n");he=me.length-1;if(he>0){be=ie+he;ve=ge-me[he].length}else{be=ie;ve=ne}we=te.comment;ie=be;pe=be;ce=ge-ve}else if(le===te.slash){ge=oe;we=le;pe=ie;ce=oe-ne;ae=ge+1}else{ge=consumeWord(ee,oe);we=te.word;pe=ie;ce=ge-ne}ae=ge+1;break}X.push([we,ie,oe-ne,pe,ce,oe,ae]);if(ve){ne=ve;ve=null}oe=ae}return X}},2684:(_,X)=>{"use strict";X.__esModule=true;X["default"]=ensureObject;function ensureObject(_){for(var X=arguments.length,ee=new Array(X>1?X-1:0),te=1;te<X;te++){ee[te-1]=arguments[te]}while(ee.length>0){var re=ee.shift();if(!_[re]){_[re]={}}_=_[re]}}_.exports=X.default},2976:(_,X)=>{"use strict";X.__esModule=true;X["default"]=getProp;function getProp(_){for(var X=arguments.length,ee=new Array(X>1?X-1:0),te=1;te<X;te++){ee[te-1]=arguments[te]}while(ee.length>0){var re=ee.shift();if(!_[re]){return undefined}_=_[re]}return _}_.exports=X.default},4513:(_,X,ee)=>{"use strict";X.__esModule=true;X.stripComments=X.ensureObject=X.getProp=X.unesc=void 0;var te=_interopRequireDefault(ee(3590));X.unesc=te["default"];var re=_interopRequireDefault(ee(2976));X.getProp=re["default"];var se=_interopRequireDefault(ee(2684));X.ensureObject=se["default"];var ne=_interopRequireDefault(ee(6453));X.stripComments=ne["default"];function _interopRequireDefault(_){return _&&_.__esModule?_:{default:_}}},6453:(_,X)=>{"use strict";X.__esModule=true;X["default"]=stripComments;function stripComments(_){var X="";var ee=_.indexOf("/*");var te=0;while(ee>=0){X=X+_.slice(te,ee);var re=_.indexOf("*/",ee+2);if(re<0){return X}te=re+2;ee=_.indexOf("/*",te)}X=X+_.slice(te);return X}_.exports=X.default},3590:(_,X)=>{"use strict";X.__esModule=true;X["default"]=unesc;function gobbleHex(_){var X=_.toLowerCase();var ee="";var te=false;for(var re=0;re<6&&X[re]!==undefined;re++){var se=X.charCodeAt(re);var ne=se>=97&&se<=102||se>=48&&se<=57;te=se===32;if(!ne){break}ee+=X[re]}if(ee.length===0){return undefined}var ie=parseInt(ee,16);var oe=ie>=55296&&ie<=57343;if(oe||ie===0||ie>1114111){return["�",ee.length+(te?1:0)]}return[String.fromCodePoint(ie),ee.length+(te?1:0)]}var ee=/\\/;function unesc(_){var X=ee.test(_);if(!X){return _}var te="";for(var re=0;re<_.length;re++){if(_[re]==="\\"){var se=gobbleHex(_.slice(re+1,re+7));if(se!==undefined){te+=se[0];re+=se[1];continue}if(_[re+1]==="\\"){te+="\\";re++;continue}if(_.length===re+1){te+=_[re]}continue}te+=_[re]}return te}_.exports=X.default},5726:_=>{"use strict";let X={comma(_){return X.split(_,[","],true)},space(_){let ee=[" ","\n","\t"];return X.split(_,ee)},split(_,X,ee){let te=[];let re="";let se=false;let ne=0;let ie=false;let oe="";let ae=false;for(let ee of _){if(ae){ae=false}else if(ee==="\\"){ae=true}else if(ie){if(ee===oe){ie=false}}else if(ee==='"'||ee==="'"){ie=true;oe=ee}else if(ee==="("){ne+=1}else if(ee===")"){if(ne>0)ne-=1}else if(ne===0){if(X.includes(ee))se=true}if(se){if(re!=="")te.push(re.trim());re="";se=false}else{re+=ee}}if(ee||re!=="")te.push(re.trim());return te}};_.exports=X;X.default=X},6124:(_,X,ee)=>{_.exports=ee(3837).deprecate},5829:_=>{function webpackEmptyContext(_){var X=new Error("Cannot find module '"+_+"'");X.code="MODULE_NOT_FOUND";throw X}webpackEmptyContext.keys=()=>[];webpackEmptyContext.resolve=webpackEmptyContext;webpackEmptyContext.id=5829;_.exports=webpackEmptyContext},1736:_=>{function webpackEmptyContext(_){var X=new Error("Cannot find module '"+_+"'");X.code="MODULE_NOT_FOUND";throw X}webpackEmptyContext.keys=()=>[];webpackEmptyContext.resolve=webpackEmptyContext;webpackEmptyContext.id=1736;_.exports=webpackEmptyContext},9613:_=>{"use strict";_.exports=require("caniuse-lite")},5591:_=>{"use strict";_.exports=require("caniuse-lite/data/features/background-clip-text")},1188:_=>{"use strict";_.exports=require("caniuse-lite/data/features/background-img-opts")},7097:_=>{"use strict";_.exports=require("caniuse-lite/data/features/border-image")},2861:_=>{"use strict";_.exports=require("caniuse-lite/data/features/border-radius")},3098:_=>{"use strict";_.exports=require("caniuse-lite/data/features/calc")},354:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-animation")},9323:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-any-link")},4773:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-appearance")},7721:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-autofill")},3166:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-backdrop-filter")},6781:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-boxdecorationbreak")},2194:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-boxshadow")},9205:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-clip-path")},2834:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-color-adjust")},8995:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-crisp-edges")},8786:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-cross-fade")},3504:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-deviceadaptation")},7801:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-element-function")},8944:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-featurequeries.js")},2416:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-file-selector-button")},1545:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-filter-function")},3882:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-filters")},2571:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-gradients")},6554:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-grid")},5197:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-hyphens")},2237:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-image-set")},7395:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-logical-props")},6649:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-masks")},8181:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-media-resolution")},3898:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-overscroll-behavior")},6215:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-placeholder")},9278:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-placeholder-shown")},2478:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-read-only-write")},1949:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-regions")},4822:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-selection")},5460:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-shapes")},1340:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-snappoints")},8235:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-sticky")},2807:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-text-align-last")},4838:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-text-orientation")},9290:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-text-spacing")},40:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-transitions")},7511:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-unicode-bidi")},7437:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-width-stretch")},2298:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css-writing-mode")},6597:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css3-boxsizing")},2983:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css3-cursors-grab")},8265:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css3-cursors-newer")},3247:_=>{"use strict";_.exports=require("caniuse-lite/data/features/css3-tabsize")},4618:_=>{"use strict";_.exports=require("caniuse-lite/data/features/flexbox")},1328:_=>{"use strict";_.exports=require("caniuse-lite/data/features/font-feature")},3944:_=>{"use strict";_.exports=require("caniuse-lite/data/features/font-kerning")},7766:_=>{"use strict";_.exports=require("caniuse-lite/data/features/fullscreen")},5691:_=>{"use strict";_.exports=require("caniuse-lite/data/features/intrinsic-width")},7809:_=>{"use strict";_.exports=require("caniuse-lite/data/features/multicolumn")},1480:_=>{"use strict";_.exports=require("caniuse-lite/data/features/object-fit")},1014:_=>{"use strict";_.exports=require("caniuse-lite/data/features/pointer")},134:_=>{"use strict";_.exports=require("caniuse-lite/data/features/text-decoration")},5514:_=>{"use strict";_.exports=require("caniuse-lite/data/features/text-emphasis")},7806:_=>{"use strict";_.exports=require("caniuse-lite/data/features/text-overflow")},744:_=>{"use strict";_.exports=require("caniuse-lite/data/features/text-size-adjust")},4602:_=>{"use strict";_.exports=require("caniuse-lite/data/features/transforms2d")},2866:_=>{"use strict";_.exports=require("caniuse-lite/data/features/transforms3d")},9474:_=>{"use strict";_.exports=require("caniuse-lite/data/features/user-select-none")},7147:_=>{"use strict";_.exports=require("fs")},4907:_=>{"use strict";_.exports=require("next/dist/compiled/browserslist")},2045:_=>{"use strict";_.exports=require("next/dist/compiled/postcss-value-parser")},1017:_=>{"use strict";_.exports=require("path")},977:_=>{"use strict";_.exports=require("postcss")},6224:_=>{"use strict";_.exports=require("tty")},7310:_=>{"use strict";_.exports=require("url")},3837:_=>{"use strict";_.exports=require("util")},5378:(_,X,ee)=>{"use strict";var te=ee(5449),re=ee(2045);function t(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var se=t(te),ne=t(re);
/**
 * Simple matrix (and vector) multiplication
 * Warning: No error handling for incompatible dimensions!
 * <AUTHOR> Verou 2020 MIT License
 *
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js
 */function a(_,X){const ee=_.length;let te,re;te=Array.isArray(_[0])?_:[_],Array.isArray(X[0])||(re=X.map((_=>[_])));const se=re[0].length,ne=re[0].map(((_,X)=>re.map((_=>_[X]))));let ie=te.map((_=>ne.map((X=>Array.isArray(_)?_.reduce(((_,ee,te)=>_+ee*(X[te]||0)),0):X.reduce(((X,ee)=>X+ee*_),0)))));return 1===ee&&(ie=ie[0]),1===se?ie.map((_=>_[0])):ie}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function o(_){return _.map((function(_){const X=_<0?-1:1,ee=Math.abs(_);return ee<.04045?_/12.92:X*Math.pow((ee+.055)/1.055,2.4)}))}function s(_){return _.map((function(_){const X=_<0?-1:1,ee=Math.abs(_);return ee>.0031308?X*(1.055*Math.pow(ee,1/2.4)-.055):12.92*_}))}function c(_){return a([[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],_)}function i(_){return a([[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]],_)}function l(_){return a([[.9554734527042182,-.023098536874261423,.0632593086610217],[-.028369706963208136,1.0099954580058226,.021041398966943008],[.012314001688319899,-.020507696433477912,1.3303659366080753]],_)}function p(_){const X=a([[.8190224432164319,.3619062562801221,-.12887378261216414],[.0329836671980271,.9292868468965546,.03614466816999844],[.048177199566046255,.26423952494422764,.6335478258136937]],_);return a([[.2104542553,.793617785,-.0040720468],[1.9779984951,-2.428592205,.4505937099],[.0259040371,.7827717662,-.808675766]],X.map((_=>Math.cbrt(_))))}function f(_){const X=a([[.9999999984505198,.39633779217376786,.2158037580607588],[1.0000000088817609,-.10556134232365635,-.06385417477170591],[1.0000000546724108,-.08948418209496575,-1.2914855378640917]],_);return a([[1.2268798733741557,-.5578149965554813,.28139105017721583],[-.04057576262431372,1.1122868293970594,-.07171106666151701],[-.07637294974672142,-.4214933239627914,1.5869240244272418]],X.map((_=>_**3)))}function d(_){const X=180*Math.atan2(_[2],_[1])/Math.PI;return[_[0],Math.sqrt(_[1]**2+_[2]**2),X>=0?X:X+360]}function v(_){return[_[0],_[1]*Math.cos(_[2]*Math.PI/180),_[1]*Math.sin(_[2]*Math.PI/180)]}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js
 */function h(_,X){const[ee,te,re]=_,[se,ne,ie]=X,oe=ee-se,ae=te-ne,le=re-ie;return Math.sqrt(oe**2+ae**2+le**2)}function m(_,X,ee){return function(_,X,ee){let te=0,re=_[1];const se=_;for(;re-te>1e-4;){const _=b(X(se));h(v(se),v(ee(_)))-.02<1e-4?te=se[1]:re=se[1],se[1]=(re+te)/2}return b(X([...se]))}(_,X,ee)}function b(_){return _.map((_=>_<0?0:_>1?1:_))}function y(_){const[X,ee,te]=_;return X>=-1e-4&&X<=1.0001&&ee>=-1e-4&&ee<=1.0001&&te>=-1e-4&&te<=1.0001}function g(_){let X=_.slice();X=X.map((function(_){const X=_<0?-1:1,ee=Math.abs(_);return X*Math.pow(ee,563/256)})),X=a([[.5766690429101305,.1855582379065463,.1882286462349947],[.29734497525053605,.6273635662554661,.07529145849399788],[.02703136138641234,.07068885253582723,.9913375368376388]],X);let ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function x(_){let X=_.slice();X=l(X);let ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function M(_){let X=_.slice(),ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function w(_){let X=_.slice();X=o(X),X=a([[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],X);let ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function I(_){let X=_.slice();X=X.map((function(_){const X=_<0?-1:1;return Math.abs(_)<=.03125?_/16:X*Math.pow(_,1.8)})),X=a([[.7977604896723027,.13518583717574031,.0313493495815248],[.2880711282292934,.7118432178101014,8565396060525902e-20],[0,0,.8251046025104601]],X),X=l(X);let ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function S(_){let X=_.slice();X=function(_){const X=1.09929682680944;return _.map((function(_){const ee=_<0?-1:1,te=Math.abs(_);return te<.08124285829863151?_/4.5:ee*Math.pow((te+X-1)/X,1/.45)}))}(X),X=a([[.6369580483012914,.14461690358620832,.1688809751641721],[.2627002120112671,.6779980715188708,.05930171646986196],[0,.028072693049087428,1.060985057710791]],X);let ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function k(_){let X=_.slice();X=c(X);let ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function P(_){let X=_.slice();X=o(X),X=c(X);let ee=X.slice();return ee=p(ee),ee=d(ee),ee[0]<1e-6&&(ee=[0,0,0]),ee[0]>.999999&&(ee=[1,0,0]),X=i(X),X=s(X),y(X)?b(X):m(ee,(_=>s(_=i(_=f(_=v(_))))),(_=>d(_=p(_=c(_=o(_))))))}function E(_,X,ee,te){const re=ne.default.stringify(_),se=_.value,ie=_.nodes.slice().filter((_=>"comment"!==_.type&&"space"!==_.type));let oe,ae=null;if("color"===se&&(ae=function(_){if(!function(_){if(!_||"word"!==_.type)return!1;switch(_.value){case"srgb":case"srgb-linear":case"display-p3":case"a98-rgb":case"prophoto-rgb":case"rec2020":case"xyz-d50":case"xyz-d65":case"xyz":return!0;default:return!1}}(_[0]))return null;const X={colorSpace:_[0].value,colorSpaceNode:_[0],parameters:[]};for(let ee=1;ee<_.length;ee++)if(F(_[ee]))X.slash=_[ee];else{if(X.slash&&(z(_[ee])||A(_[ee])||q(_[ee]))){X.alpha=_[ee];break}if(!z(_[ee]))return null;{const te=ne.default.unit(_[ee].value);"%"===te.unit&&(te.number=String(parseFloat(te.number)/100),te.unit="",_[ee].value=String(te.number)),X.parameters.push({value:te,node:_[ee]})}}if(0===X.parameters.length)return X;X.parameters.length<3&&(X.parameters=[...X.parameters,{node:{sourceIndex:0,sourceEndIndex:1,value:"0",type:"word"},value:{number:"0",unit:""}},{node:{sourceIndex:0,sourceEndIndex:1,value:"0",type:"word"},value:{number:"0",unit:""}}]);X.parameters.length>3&&(X.parameters=X.parameters.slice(0,3));return X}(ie)),!ae)return;switch(_.value="rgb",function(_,X,ee){if(!X||!ee)return;if(_.value="rgba",X.value=",",X.before="",!function(_){if(!_||"word"!==_.type)return!1;if(!O(_))return!1;const X=ne.default.unit(_.value);if(!X)return!1;return!!X.number}(ee))return;const te=ne.default.unit(ee.value);if(!te)return;"%"===te.unit&&(te.number=String(parseFloat(te.number)/100),ee.value=String(te.number))}(_,ae.slash,ae.alpha),ae.colorSpace){case"srgb":oe=P;break;case"srgb-linear":oe=k;break;case"a98-rgb":oe=g;break;case"prophoto-rgb":oe=I;break;case"display-p3":oe=w;break;case"rec2020":oe=S;break;case"xyz-d50":oe=x;break;case"xyz-d65":case"xyz":oe=M;break;default:return}const le=(ue=ae,ue.parameters.map((_=>_.value))).map((_=>parseFloat(_.number)));var ue;const ce=oe(le);!y(le)&&te&&X.warn(ee,`"${re}" is out of gamut for "${ae.colorSpace}". Given "preserve: true" is set, this will lead to unexpected results in some browsers.`),_.nodes=[{sourceIndex:0,sourceEndIndex:1,value:String(Math.round(255*ce[0])),type:"word"},{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""},{sourceIndex:0,sourceEndIndex:1,value:String(Math.round(255*ce[1])),type:"word"},{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""},{sourceIndex:0,sourceEndIndex:1,value:String(Math.round(255*ce[2])),type:"word"}],ae.alpha&&(_.nodes.push({sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),_.nodes.push(ae.alpha))}function z(_){if(!_||"word"!==_.type)return!1;if(!O(_))return!1;const X=ne.default.unit(_.value);return!!X&&("%"===X.unit||""===X.unit)}function A(_){return _&&"function"===_.type&&"calc"===_.value}function q(_){return _&&"function"===_.type&&"var"===_.value}function F(_){return _&&"div"===_.type&&"/"===_.value}function O(_){if(!_||!_.value)return!1;try{return!1!==ne.default.unit(_.value)}catch(_){return!1}}const j=_=>{const X="preserve"in Object(_)&&Boolean(_.preserve);return{postcssPlugin:"postcss-color-function",Declaration:(_,{result:ee})=>{if(function(_){const X=_.parent;if(!X)return!1;const ee=X.index(_);for(let te=0;te<ee;te++){const ee=X.nodes[te];if("decl"===ee.type&&ee.prop===_.prop)return!0}return!1}(_))return;if(function(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("supports"===X.name&&-1!==X.params.indexOf("color("))return!0;X=X.parent}else X=X.parent;return!1}(_))return;const te=_.value;if(-1===te.indexOf("color("))return;const re=function(_,X,ee,te){let re;try{re=ne.default(_)}catch(te){X.warn(ee,`Failed to parse value '${_}' as a color function. Leaving the original value intact.`)}if(void 0===re)return;re.walk((_=>{_.type&&"function"===_.type&&"color"===_.value&&E(_,X,ee,te)}));const se=String(re);return se!==_?se:void 0}(te,_,ee,X);void 0!==re&&(X?_.cloneBefore({value:re}):_.value=re)}}};j.postcss=!0;const $=_=>{const X=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},_);return X.enableProgressiveCustomProperties&&X.preserve?{postcssPlugin:"postcss-color-function",plugins:[se.default(),j(X)]}:j(X)};$.postcss=!0,_.exports=$},3345:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045));const re=["woff","truetype","opentype","woff2","embedded-opentype","collection","svg"],r=_=>{const X="preserve"in Object(_)&&Boolean(_.preserve);return{postcssPlugin:"postcss-font-format-keywords",AtRule:{"font-face"(_){"font-face"===_.name&&_.walkDecls("src",(_=>{if(!_.value.includes("format("))return;const ee=te.default(_.value);ee.walk((_=>{"function"===_.type&&"format"===_.value&&_.nodes.forEach((_=>{"word"===_.type&&re.includes(_.value)&&(_.value=te.default.stringify({type:"string",value:_.value,quote:'"'}))}))})),X?_.cloneBefore({value:ee.toString()}):_.value=ee.toString()}))}}}};r.postcss=!0,_.exports=r},434:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045));function t(_){const X=_[0];let ee=_[1],te=_[2];if(ee/=100,te/=100,ee+te>=1){const _=ee/(ee+te);return[_,_,_]}const re=function(_){let X=_[0],ee=_[1],te=_[2];X%=360,X<0&&(X+=360);function u(_){const re=(_+X/30)%12,se=ee*Math.min(te,1-te);return te-se*Math.max(-1,Math.min(re-3,9-re,1))}return ee/=100,te/=100,[u(0),u(8),u(4)]}([X,100,50]);for(let _=0;_<3;_++)re[_]*=1-ee-te,re[_]+=ee;return re.map((_=>Math.round(255*_)))}function r(_){const X=_.nodes.slice().filter((_=>"comment"!==_.type&&"space"!==_.type)),ee=function(_){if(!function(_){if(!_||"word"!==_.type)return!1;if(!o(_))return!1;const X=te.default.unit(_.value);if(!X)return!1;return!!X.number&&("deg"===X.unit||"grad"===X.unit||"rad"===X.unit||"turn"===X.unit||""===X.unit)}(_[0]))return null;if(!u(_[1]))return null;if(!u(_[2]))return null;const X={h:te.default.unit(_[0].value),hNode:_[0],w:te.default.unit(_[1].value),wNode:_[1],b:te.default.unit(_[2].value),bNode:_[2]};if(function(_){switch(_.unit){case"deg":return void(_.unit="");case"rad":return _.unit="",void(_.number=(180*parseFloat(_.number)/Math.PI).toString());case"grad":return _.unit="",void(_.number=(.9*parseFloat(_.number)).toString());case"turn":_.unit="",_.number=(360*parseFloat(_.number)).toString()}}(X.h),""!==X.h.unit)return null;i(X.w),i(X.b),function(_){return _&&"div"===_.type&&"/"===_.value}(_[3])&&(X.slash=_[3]);(u(_[4])||function(_){return _&&"function"===_.type&&"calc"===_.value}(_[4])||function(_){return _&&"function"===_.type&&"var"===_.value}(_[4]))&&(X.alpha=_[4]);return X}(X);if(!ee)return;if(X.length>3&&(!ee.slash||!ee.alpha))return;_.value="rgb",function(_,X,ee){if(!X||!ee)return;if(_.value="rgba",X.value=",",X.before="",!function(_){if(!_||"word"!==_.type)return!1;if(!o(_))return!1;const X=te.default.unit(_.value);if(!X)return!1;return!!X.number}(ee))return;const re=te.default.unit(ee.value);if(!re)return;"%"===re.unit&&(re.number=String(parseFloat(re.number)/100),ee.value=String(re.number))}(_,ee.slash,ee.alpha);const[re,se,ne]=[(ie=ee).hNode,ie.wNode,ie.bNode];var ie;const[oe,ae,le]=function(_){return[_.h,_.w,_.b]}(ee),ue=t([oe.number,ae.number,le.number].map((_=>parseFloat(_))));_.nodes.splice(_.nodes.indexOf(re)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),_.nodes.splice(_.nodes.indexOf(se)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),a(_.nodes,re,{...re,value:String(ue[0])}),a(_.nodes,se,{...se,value:String(ue[1])}),a(_.nodes,ne,{...ne,value:String(ue[2])})}function u(_){if(!_||"word"!==_.type)return!1;if(!o(_))return!1;const X=te.default.unit(_.value);return!!X&&("%"===X.unit||""===X.unit)}function a(_,X,ee){const te=_.indexOf(X);_[te]=ee}function i(_){if("%"!==_.unit)return _.unit="%",void(_.number=(100*parseFloat(_.number)).toString())}function o(_){if(!_||!_.value)return!1;try{return!1!==te.default.unit(_.value)}catch(_){return!1}}const l=_=>{const X="preserve"in Object(_)&&Boolean(_.preserve);return{postcssPlugin:"postcss-hwb-function",Declaration:(_,{result:ee,postcss:re})=>{if(X&&function(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("supports"===X.name&&-1!==X.params.indexOf("(color: hwb(0% 0 0))"))return!0;X=X.parent}else X=X.parent;return!1}(_))return;const se=_.value;if(!se.includes("hwb"))return;const ne=function(_,X,ee){let re;try{re=te.default(_)}catch(te){X.warn(ee,`Failed to parse value '${_}' as a hwb function. Leaving the original value intact.`)}if(void 0===re)return;re.walk((_=>{_.type&&"function"===_.type&&"hwb"===_.value&&r(_)}));const se=String(re);if(se===_)return;return se}(se,_,ee);if(void 0!==ne)if(_.variable&&X){const X=_.parent,ee=re.atRule({name:"supports",params:"(color: hwb(0% 0 0))",source:_.source}),te=X.clone();te.removeAll(),te.append(_.clone()),ee.append(te),function(_,X,ee){let te=X,re=X.next();for(;te&&re&&"atrule"===re.type&&"supports"===re.name&&re.params===ee;)te=re,re=re.next();te.after(_)}(ee,X,"(color: hwb(0% 0 0))"),_.value=ne}else X?_.cloneBefore({value:ne}):_.value=ne}}};l.postcss=!0,_.exports=l},1758:(_,X,ee)=>{"use strict";var te=ee(5449),re=ee(2045);function t(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var se=t(te),ne=t(re);const n=_=>({postcssPlugin:"postcss-ic-unit",Declaration(X){if(!X.value.includes("ic"))return;if(function(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("supports"===X.name&&/\(font-size: \d+ic\)/.test(X.params))return!0;X=X.parent}else X=X.parent;return!1}(X))return;const ee=ne.default(X.value);ee.walk((_=>{if(!_.type||"word"!==_.type)return;const X=ne.default.unit(_.value);X&&"ic"===X.unit&&(_.value=`${X.number}em`)}));const te=String(ee);te!==X.value&&(_.preserve?X.cloneBefore({value:te}):X.value=te)}});n.postcss=!0;const o=_=>{const X=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},_);return X.enableProgressiveCustomProperties&&X.preserve?{postcssPlugin:"postcss-ic-unit",plugins:[se.default(),n(X)]}:n(X)};o.postcss=!0,_.exports=o},2238:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));function n(_){_&&_.nodes&&_.nodes.sort(((_,X)=>"selector"===_.type&&"selector"===X.type&&_.nodes.length&&X.nodes.length?o(_.nodes[0].value,_.nodes[0].type)-o(X.nodes[0].value,X.nodes[0].type):"selector"===_.type&&_.nodes.length?o(_.nodes[0].value,_.nodes[0].type)-o(X.value,X.type):"selector"===X.type&&X.nodes.length?o(_.value,_.type)-o(X.nodes[0].value,X.nodes[0].type):o(_.value,_.type)-o(X.value,X.type)))}function o(_,X){return"pseudo"===X&&_&&0===_.indexOf("::")?re.pseudoElement:re[X]}const re={universal:0,tag:1,id:2,class:3,attribute:4,selector:5,pseudo:6,pseudoElement:7,string:8,root:9,comment:10};function d(_,X,ee){return _.flatMap((_=>{if(-1===_.indexOf(":-csstools-matches")&&-1===_.indexOf(":is"))return _;const re=te.default().astSync(_);return re.walkPseudos((_=>{if(":is"===_.value&&_.nodes&&_.nodes.length&&"selector"===_.nodes[0].type&&0===_.nodes[0].nodes.length)return _.value=":not",void _.nodes[0].append(te.default.universal());if(":-csstools-matches"===_.value)if(!_.nodes||_.nodes.length){if(1===_.nodes.length&&"selector"===_.nodes[0].type){if(1===_.nodes[0].nodes.length)return void _.replaceWith(_.nodes[0].nodes[0]);if(!_.nodes[0].some((_=>"combinator"===_.type)))return void _.replaceWith(..._.nodes[0].nodes)}1!==re.nodes.length||"selector"!==re.nodes[0].type||1!==re.nodes[0].nodes.length||re.nodes[0].nodes[0]!==_?function(_){return!(!_||!_.nodes||"selector"!==_.type||3!==_.nodes.length||!_.nodes[0]||"pseudo"!==_.nodes[0].type||":-csstools-matches"!==_.nodes[0].value||!_.nodes[1]||"combinator"!==_.nodes[1].type||"+"!==_.nodes[1].value||!_.nodes[2]||"pseudo"!==_.nodes[2].type||":-csstools-matches"!==_.nodes[2].value||!_.nodes[0].nodes||1!==_.nodes[0].nodes.length||"selector"!==_.nodes[0].nodes[0].type||!_.nodes[0].nodes[0].nodes||3!==_.nodes[0].nodes[0].nodes.length||!_.nodes[0].nodes[0].nodes||"combinator"!==_.nodes[0].nodes[0].nodes[1].type||">"!==_.nodes[0].nodes[0].nodes[1].value||!_.nodes[2].nodes||1!==_.nodes[2].nodes.length||"selector"!==_.nodes[2].nodes[0].type||!_.nodes[2].nodes[0].nodes||3!==_.nodes[2].nodes[0].nodes.length||!_.nodes[2].nodes[0].nodes||"combinator"!==_.nodes[2].nodes[0].nodes[1].type||">"!==_.nodes[2].nodes[0].nodes[1].value||(_.nodes[0].nodes[0].insertAfter(_.nodes[0].nodes[0].nodes[0],_.nodes[2].nodes[0].nodes[0].clone()),_.nodes[2].nodes[0].nodes[1].remove(),_.nodes[2].nodes[0].nodes[0].remove(),_.nodes[0].replaceWith(_.nodes[0].nodes[0]),_.nodes[2].replaceWith(_.nodes[2].nodes[0]),0))}(_.parent)||function(_){if(!_||!_.nodes)return!1;if("selector"!==_.type)return!1;if(2!==_.nodes.length)return!1;let X,ee;return _.nodes[0]&&"pseudo"===_.nodes[0].type&&":-csstools-matches"===_.nodes[0].value?(X=0,ee=1):_.nodes[1]&&"pseudo"===_.nodes[1].type&&":-csstools-matches"===_.nodes[1].value&&(X=1,ee=0),!(!X||!_.nodes[ee]||"selector"===_.nodes[ee].type&&_.nodes[ee].some((_=>"combinator"===_.type))||(_.nodes[X].append(_.nodes[ee].clone()),_.nodes[X].replaceWith(..._.nodes[X].nodes),_.nodes[ee].remove(),0))}(_.parent)||("warning"===X.onComplexSelector&&ee(),_.value=":is"):_.replaceWith(..._.nodes[0].nodes)}else _.remove()})),re.walk((_=>{"selector"===_.type&&"nodes"in _&&1===_.nodes.length&&"selector"===_.nodes[0].type&&_.replaceWith(_.nodes[0])})),re.walk((_=>{"nodes"in _&&function(_){if(!_||!_.nodes)return;let X=[];const ee=[..._.nodes];for(let _=0;_<ee.length+1;_++){const re=ee[_];if(re&&"combinator"!==re.type)X.push(re);else{if(X.length>1){const _=te.default.selector({value:""});X[0].replaceWith(_),X.slice(1).forEach((_=>{_.remove()})),X.forEach((X=>{_.append(X)})),n(_),_.replaceWith(..._.nodes)}X=[]}}}(_)})),re.toString()})).filter((_=>!!_))}function l(_){let X=0,ee=0,re=0;if("universal"==_.type)return{a:0,b:0,c:0};if("id"===_.type)X+=1;else if("tag"===_.type)re+=1;else if("class"===_.type)ee+=1;else if("attribute"===_.type)ee+=1;else if("pseudo"===_.type&&0===_.value.indexOf("::"))re+=1;else if("pseudo"===_.type)switch(_.value){case":after":case":before":re+=1;break;case":is":case":has":case":not":if(_.nodes&&_.nodes.length>0){let te={a:0,b:0,c:0};_.nodes.forEach((_=>{const X=l(_);X.a>te.a?te=X:X.a<te.a||(X.b>te.b?te=X:X.b<te.b||X.c>te.c&&(te=X))})),X+=te.a,ee+=te.b,re+=te.c}break;case"where":break;case":nth-child":case":nth-last-child":{const se=_.nodes.findIndex((_=>{_.value}));if(se>-1){const ne=l(te.default.selector({nodes:_.nodes.slice(se+1),value:""}));X+=ne.a,ee+=ne.b,re+=ne.c}else X+=X,ee+=ee,re+=re}break;default:ee+=1}else _.nodes&&_.nodes.length>0&&_.nodes.forEach((_=>{const te=l(_);X+=te.a,ee+=te.b,re+=te.c}));return{a:X,b:ee,c:re}}function r(_,X,ee=0){const re=":not(#"+X.specificityMatchingName+")",se=":not(."+X.specificityMatchingName+")",ne=":not("+X.specificityMatchingName+")";return _.flatMap((_=>{if(-1===_.indexOf(":is"))return _;let ie=!1;const oe=[];if(te.default().astSync(_).walkPseudos((_=>{if(":is"!==_.value||!_.nodes||!_.nodes.length)return;if("selector"===_.nodes[0].type&&0===_.nodes[0].nodes.length)return;let X=_.parent;for(;X;){if(":is"===X.value&&"pseudo"===X.type)return void(ie=!0);X=X.parent}const ee=l(_),te=_.sourceIndex,ae=te+_.toString().length,le=[];_.nodes.forEach((_=>{const X={start:te,end:ae,option:""},ie=l(_);let oe=_.toString().trim();const ue=Math.max(0,ee.a-ie.a),ce=Math.max(0,ee.b-ie.b),pe=Math.max(0,ee.c-ie.c);for(let _=0;_<ue;_++)oe+=re;for(let _=0;_<ce;_++)oe+=se;for(let _=0;_<pe;_++)oe+=ne;X.option=oe,le.push(X)})),oe.push(le)})),!oe.length)return[_];let ae=[];return function(..._){const X=[],ee=_.length-1;function o(te,re){for(let se=0,ne=_[re].length;se<ne;se++){const ne=te.slice(0);ne.push(_[re][se]),re==ee?X.push(ne):o(ne,re+1)}}return o([],0),X}(...oe).forEach((X=>{let ee="";for(let re=0;re<X.length;re++){var te;const se=X[re];ee+=_.substring((null==(te=X[re-1])?void 0:te.end)||0,X[re].start),ee+=":-csstools-matches("+se.option+")",re===X.length-1&&(ee+=_.substring(X[re].end))}ae.push(ee)})),ie&&ee<10&&(ae=r(ae,X,ee+1)),ae})).filter((_=>!!_))}const c=_=>{const X={specificityMatchingName:"does-not-exist",..._||{}};return{postcssPlugin:"postcss-is-pseudo-class",Rule(_,{result:ee}){if(!_.selector)return;if(-1===_.selector.indexOf(":is"))return;let te=!1;const t=()=>{"warning"===X.onComplexSelector&&(te||(te=!0,_.warn(ee,`Complex selectors in '${_.selector}' can not be transformed to an equivalent selector without ':is()'.`)))};try{let ee=!1;const te=[],re=d(r(_.selectors,{specificityMatchingName:X.specificityMatchingName}),{onComplexSelector:X.onComplexSelector},t);if(Array.from(new Set(re)).forEach((X=>{_.selectors.indexOf(X)>-1?te.push(X):(_.cloneBefore({selector:X}),ee=!0)})),te.length&&ee&&_.cloneBefore({selectors:te}),!X.preserve){if(!ee)return;_.remove()}}catch(X){if(X.message.indexOf("call stack size exceeded")>-1)throw X;_.warn(ee,`Failed to parse selector "${_.selector}"`)}}}};c.postcss=!0,_.exports=c},3942:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045)),re=new Map([["block,flow","block"],["block,flow-root","flow-root"],["inline,flow","inline"],["inline,flow-root","inline-block"],["run-in,flow","run-in"],["list-item,block,flow","list-item"],["inline,flow,list-item","inline list-item"],["block,flex","flex"],["inline,flex","inline-flex"],["block,grid","grid"],["inline,grid","inline-grid"],["inline,ruby","ruby"],["block,table","table"],["inline,table","inline-table"],["table-cell,flow","table-cell"],["table-caption,flow","table-caption"],["ruby-base,flow","ruby-base"],["ruby-text,flow","ruby-text"]]);const n=_=>{const X=!("preserve"in Object(_))||Boolean(_.preserve);return{postcssPlugin:"postcss-normalize-display-values",prepare(){const _=new Map;return{Declaration:{display(ee){const se=ee.value;if(!se)return;if(_.has(se))return void(ee.value!==_.get(se)&&(X?ee.cloneBefore({value:_.get(se)}):ee.value=_.get(se)));const ne=function(_){const{nodes:X}=te.default(_);if(1===X.length)return _;const ee=X.filter((_=>"word"===_.type)).map((_=>_.value.toLowerCase()));if(ee.length<=1)return _;return re.get(ee.join(","))||_}(se);ee.value!==ne&&(X?ee.cloneBefore({value:ne}):ee.value=ne),_.set(se,ne)}}}}}};n.postcss=!0,_.exports=n},8078:(_,X,ee)=>{"use strict";var te=ee(5449),re=ee(2045);function t(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var se=t(te),ne=t(re);
/**
 * Simple matrix (and vector) multiplication
 * Warning: No error handling for incompatible dimensions!
 * <AUTHOR> Verou 2020 MIT License
 *
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js
 */function a(_,X){const ee=_.length;let te,re;te=Array.isArray(_[0])?_:[_],Array.isArray(X[0])||(re=X.map((_=>[_])));const se=re[0].length,ne=re[0].map(((_,X)=>re.map((_=>_[X]))));let ie=te.map((_=>ne.map((X=>Array.isArray(_)?_.reduce(((_,ee,te)=>_+ee*(X[te]||0)),0):X.reduce(((X,ee)=>X+ee*_),0)))));return 1===ee&&(ie=ie[0]),1===se?ie.map((_=>_[0])):ie}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function o(_){return _.map((function(_){const X=_<0?-1:1,ee=Math.abs(_);return ee<.04045?_/12.92:X*Math.pow((ee+.055)/1.055,2.4)}))}function i(_){return _.map((function(_){const X=_<0?-1:1,ee=Math.abs(_);return ee>.0031308?X*(1.055*Math.pow(ee,1/2.4)-.055):12.92*_}))}function l(_){return a([[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],_)}function s(_){return a([[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]],_)}function c(_){return o(_)}function f(_){return i(_)}function p(_){return a([[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],_)}function d(_){return a([[2.493496911941425,-.9313836179191239,-.40271078445071684],[-.8294889695615747,1.7626640603183463,.023624685841943577],[.03584583024378447,-.07617238926804182,.9568845240076872]],_)}function b(_){const X=a([[.8190224432164319,.3619062562801221,-.12887378261216414],[.0329836671980271,.9292868468965546,.03614466816999844],[.048177199566046255,.26423952494422764,.6335478258136937]],_);return a([[.2104542553,.793617785,-.0040720468],[1.9779984951,-2.428592205,.4505937099],[.0259040371,.7827717662,-.808675766]],X.map((_=>Math.cbrt(_))))}function v(_){const X=a([[.9999999984505198,.39633779217376786,.2158037580607588],[1.0000000088817609,-.10556134232365635,-.06385417477170591],[1.0000000546724108,-.08948418209496575,-1.2914855378640917]],_);return a([[1.2268798733741557,-.5578149965554813,.28139105017721583],[-.04057576262431372,1.1122868293970594,-.07171106666151701],[-.07637294974672142,-.4214933239627914,1.5869240244272418]],X.map((_=>_**3)))}function m(_){const X=180*Math.atan2(_[2],_[1])/Math.PI;return[_[0],Math.sqrt(_[1]**2+_[2]**2),X>=0?X:X+360]}function h(_){return[_[0],_[1]*Math.cos(_[2]*Math.PI/180),_[1]*Math.sin(_[2]*Math.PI/180)]}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js
 */function y(_,X){const[ee,te,re]=_,[se,ne,ie]=X,oe=ee-se,ae=te-ne,le=re-ie;return Math.sqrt(oe**2+ae**2+le**2)}function g(_,X,ee){return function(_,X,ee){let te=0,re=_[1];const se=_;for(;re-te>1e-4;){const _=x(X(se));y(h(se),h(ee(_)))-.02<1e-4?te=se[1]:re=se[1],se[1]=(re+te)/2}return x(X([...se]))}(_,X,ee)}function x(_){return _.map((_=>_<0?0:_>1?1:_))}function F(_){const[X,ee,te]=_;return X>=-1e-4&&X<=1.0001&&ee>=-1e-4&&ee<=1.0001&&te>=-1e-4&&te<=1.0001}function M(_){const[X,ee,te]=_;let re=[Math.max(X,0),ee,te],se=m(re);return se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),re=v(re),re=d(re),re=f(re),F(re)?[x(re),!0]:[g(se,(_=>f(_=d(_=v(_=h(_))))),(_=>m(_=b(_=p(_=c(_)))))),!1]}function k(_){const[X,ee,te]=_,re=[Math.max(X,0),ee,te%360];let se=re;return se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),se=h(se),se=v(se),se=d(se),se=f(se),F(se)?[x(se),!0]:[g(re,(_=>f(_=d(_=v(_=h(_))))),(_=>m(_=b(_=p(_=c(_)))))),!1]}function P(_){const[X,ee,te]=_;let re=[Math.max(X,0),ee,te],se=m(re);return se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),re=v(re),re=s(re),re=i(re),F(re)?x(re).map((_=>Math.round(255*_))):g(se,(_=>i(_=s(_=v(_=h(_))))),(_=>m(_=b(_=l(_=o(_)))))).map((_=>Math.round(255*_)))}function w(_){const[X,ee,te]=_,re=[Math.max(X,0),ee,te%360];let se=re;return se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),se=h(se),se=v(se),se=s(se),se=i(se),F(se)?x(se).map((_=>Math.round(255*_))):g(re,(_=>i(_=s(_=v(_=h(_))))),(_=>m(_=b(_=l(_=o(_)))))).map((_=>Math.round(255*_)))}function I(_){const X=_.value,ee=_.nodes.slice().filter((_=>"comment"!==_.type&&"space"!==_.type));let te=null;if("oklab"===X?te=B(ee):"oklch"===X&&(te=q(ee)),!te)return;_.value="rgb",function(_,X,ee){if(!X||!ee)return;if(_.value="rgba",X.value=",",X.before="",!function(_){if(!_||"word"!==_.type)return!1;if(!D(_))return!1;const X=ne.default.unit(_.value);if(!X)return!1;return!!X.number}(ee))return;const te=ne.default.unit(ee.value);if(!te)return;"%"===te.unit&&(te.number=String(parseFloat(te.number)/100),ee.value=String(te.number))}(_,te.slash,te.alpha);const[re,se,ie]=j(te),[oe,ae,le]=C(te),ue=("oklab"===X?P:w)([oe.number,ae.number,le.number].map((_=>parseFloat(_))));_.nodes.splice(_.nodes.indexOf(re)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),_.nodes.splice(_.nodes.indexOf(se)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),$(_.nodes,re,{...re,value:String(ue[0])}),$(_.nodes,se,{...se,value:String(ue[1])}),$(_.nodes,ie,{...ie,value:String(ue[2])})}function N(_){if(!_||"word"!==_.type)return!1;if(!D(_))return!1;const X=ne.default.unit(_.value);return!!X&&("%"===X.unit||""===X.unit)}function S(_){return _&&"function"===_.type&&"calc"===_.value}function O(_){return _&&"function"===_.type&&"var"===_.value}function A(_){return _&&"div"===_.type&&"/"===_.value}function q(_){if(!N(_[0]))return null;if(!N(_[1]))return null;if(!function(_){if(!_||"word"!==_.type)return!1;if(!D(_))return!1;const X=ne.default.unit(_.value);return!(!X||!X.number||"deg"!==X.unit&&"grad"!==X.unit&&"rad"!==X.unit&&"turn"!==X.unit&&""!==X.unit)}(_[2]))return null;const X={l:ne.default.unit(_[0].value),lNode:_[0],c:ne.default.unit(_[1].value),cNode:_[1],h:ne.default.unit(_[2].value),hNode:_[2]};return function(_){switch(_.unit){case"deg":return void(_.unit="");case"rad":return _.unit="",void(_.number=(180*parseFloat(_.number)/Math.PI).toString());case"grad":return _.unit="",void(_.number=(.9*parseFloat(_.number)).toString());case"turn":_.unit="",_.number=(360*parseFloat(_.number)).toString()}}(X.h),""!==X.h.unit?null:(A(_[3])&&(X.slash=_[3]),(N(_[4])||S(_[4])||O(_[4]))&&(X.alpha=_[4]),!(_.length>3)||X.slash&&X.alpha?("%"===X.l.unit&&(X.l.unit="",X.l.number=(parseFloat(X.l.number)/100).toFixed(10)),"%"===X.c.unit&&(X.c.unit="",X.c.number=(parseFloat(X.c.number)/100*.4).toFixed(10)),X):null)}function B(_){if(!N(_[0]))return null;if(!N(_[1]))return null;if(!N(_[2]))return null;const X={l:ne.default.unit(_[0].value),lNode:_[0],a:ne.default.unit(_[1].value),aNode:_[1],b:ne.default.unit(_[2].value),bNode:_[2]};return A(_[3])&&(X.slash=_[3]),(N(_[4])||S(_[4])||O(_[4]))&&(X.alpha=_[4]),!(_.length>3)||X.slash&&X.alpha?("%"===X.l.unit&&(X.l.unit="",X.l.number=(parseFloat(X.l.number)/100).toFixed(10)),"%"===X.a.unit&&(X.a.unit="",X.a.number=(parseFloat(X.a.number)/100*.4).toFixed(10)),"%"===X.b.unit&&(X.b.unit="",X.b.number=(parseFloat(X.b.number)/100*.4).toFixed(10)),X):null}function E(_){return void 0!==_.a}function j(_){return E(_)?[_.lNode,_.aNode,_.bNode]:[_.lNode,_.cNode,_.hNode]}function C(_){return E(_)?[_.l,_.a,_.b]:[_.l,_.c,_.h]}function $(_,X,ee){const te=_.indexOf(X);_[te]=ee}function D(_){if(!_||!_.value)return!1;try{return!1!==ne.default.unit(_.value)}catch(_){return!1}}function G(_,X,ee,te){let re;try{re=ne.default(_)}catch(te){X.warn(ee,`Failed to parse value '${_}' as an oklab or oklch function. Leaving the original value intact.`)}if(void 0===re)return;re.walk((_=>{_.type&&"function"===_.type&&("oklab"!==_.value&&"oklch"!==_.value||I(_))}));const se=String(re);if(se===_)return;const ie=ne.default(_);ie.walk((_=>{_.type&&"function"===_.type&&("oklab"!==_.value&&"oklch"!==_.value||function(_,X,ee,te){const re=ne.default.stringify(_),se=_.value,ie=_.nodes.slice().filter((_=>"comment"!==_.type&&"space"!==_.type));let oe=null;if("oklab"===se?oe=B(ie):"oklch"===se&&(oe=q(ie)),!oe)return;if(ie.length>3&&(!oe.slash||!oe.alpha))return;_.value="color";const[ae,le,ue]=j(oe),[ce,pe,fe]=C(oe),de="oklab"===se?M:k,he=[ce.number,pe.number,fe.number].map((_=>parseFloat(_))),[me,ge]=de(he);!ge&&te&&X.warn(ee,`"${re}" is out of gamut for "display-p3". Given "preserve: true" is set, this will lead to unexpected results in some browsers.`),_.nodes.splice(0,0,{sourceIndex:0,sourceEndIndex:10,value:"display-p3",type:"word"}),_.nodes.splice(1,0,{sourceIndex:0,sourceEndIndex:1,value:" ",type:"space"}),$(_.nodes,ae,{...ae,value:me[0].toFixed(5)}),$(_.nodes,le,{...le,value:me[1].toFixed(5)}),$(_.nodes,ue,{...ue,value:me[2].toFixed(5)})}(_,X,ee,te))}));return{rgb:se,displayP3:String(ie)}}const L=_=>({postcssPlugin:"postcss-oklab-function",Declaration:(X,{result:ee})=>{if(function(_){const X=_.parent;if(!X)return!1;const ee=X.index(_);for(let te=0;te<ee;te++){const ee=X.nodes[te];if("decl"===ee.type&&ee.prop===_.prop)return!0}return!1}(X))return;if(function(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("supports"===X.name){if(-1!==X.params.indexOf("oklab("))return!0;if(-1!==X.params.indexOf("oklch("))return!0}X=X.parent}else X=X.parent;return!1}(X))return;const te=X.value;if(!/(^|[^\w-])(oklab|oklch)\(/i.test(te))return;const re=G(te,X,ee,_.preserve);void 0!==re&&(_.preserve?(X.cloneBefore({value:re.rgb}),_.subFeatures.displayP3&&X.cloneBefore({value:re.displayP3})):(X.cloneBefore({value:re.rgb}),_.subFeatures.displayP3&&X.cloneBefore({value:re.displayP3}),X.remove()))}});L.postcss=!0;const z=_=>{const X=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},_);return X.subFeatures=Object.assign({displayP3:!0},X.subFeatures),X.enableProgressiveCustomProperties&&(X.preserve||X.subFeatures.displayP3)?{postcssPlugin:"postcss-oklab-function",plugins:[se.default(),L(X)]}:L(X)};z.postcss=!0,_.exports=z},5449:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045));const re=[{supports:"color(srgb 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"srgb"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"srgb"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(srgb-linear 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"srgb-linear"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"srgb-linear"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(a98-rgb 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"a98-rgb"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"a98-rgb"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(prophoto-rgb 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"prophoto-rgb"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"prophoto-rgb"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(display-p3 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"display-p3"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"display-p3"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(rec2020 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"rec2020"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"rec2020"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(xyz-d50 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"xyz-d50"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"xyz-d50"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(xyz-d65 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"xyz-d65"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"xyz-d65"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color(xyz 0 0 0)",property:"color",sniff:"color",matchers:[{type:"function",value:"color",nodes:[{type:"word",value:"xyz"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color",nodes:[{type:"word",value:"xyz"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"hsl(0, 0%, 0%)",property:"color",sniff:"hsl",matchers:[{type:"function",value:"hsl",nodes:[{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0}]}]},{supports:"hsl(0 0% 0% / 0)",property:"color",sniff:"hsl",matchers:[{type:"function",value:"hsl",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"hsl",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"hsla(0 0% 0% / 0)",property:"color",sniff:"hsla",matchers:[{type:"function",value:"hsla",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"hwb(0 0% 0%)",property:"color",sniff:"hwb",matchers:[{type:"function",value:"hwb",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"hwb",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"lab(0% 0 0)",property:"color",sniff:"lab",matchers:[{type:"function",value:"lab",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"lab",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"lch(0% 0 0)",property:"color",sniff:"lch",matchers:[{type:"function",value:"lch",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"lch",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"oklab(0% 0 0)",property:"color",sniff:"oklab",matchers:[{type:"function",value:"oklab",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"oklab",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"oklch(0% 0 0)",property:"color",sniff:"oklch",matchers:[{type:"function",value:"oklch",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"oklch",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"rgb(0, 0, 0, 0)",property:"color",sniff:"rgb",matchers:[{type:"function",value:"rgb",nodes:[{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0}]}]},{supports:"rgb(0 0 0 / 0)",property:"color",sniff:"rgb",matchers:[{type:"function",value:"rgb",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"rgb",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"rgba(0 0 0 / 0)",property:"color",sniff:"rgba",matchers:[{type:"function",value:"rgba",nodes:[{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:"/"},{type:"word",isVariable:!0}]}]},{supports:"color-mix(in oklch, #000, #fff)",property:"color",sniff:"color-mix",matchers:[{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0}]},{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0}]},{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0}]},{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0}]},{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]},{type:"function",value:"color-mix",nodes:[{type:"word",value:"in"},{type:"space"},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0},{type:"div",value:","},{type:"word",isVariable:!0},{type:"space"},{type:"word",isVariable:!0}]}]},{supports:"1ic",property:"font-size",sniff:"ic",matchers:[{type:"word",value:"1ic",dimension:{unit:"ic"}}]}];function p(_,X){if(_.isVariable&&X)return!0;if(_.type!==X.type)return!1;if(function(_,X){if("space"===_.type&&"space"===X.type&&_.value.trim()===X.value.trim())return!1;if(_.dimension&&X.dimension)return _.dimension.unit!==X.dimension.unit;if(_.value!==X.value)return!0;return!1}(_,X))return!1;if(_.nodes&&X.nodes){for(let ee=0;ee<_.nodes.length;ee++){let te=ee,re=ee;for(;_.nodes[te]&&"space"===_.nodes[te].type;)te++;for(;X.nodes[re]&&"space"===X.nodes[re].type;)re++;if(!!_.nodes[te]!=!!X.nodes[re])return!1;if(!p(_.nodes[te],X.nodes[re]))return!1}return!0}return!0}const se=["at","bottom","center","circle","closest-corner","closest-side","ellipse","farthest-corner","farthest-side","from","in","left","right","to","top"];function i(_){const X=[],ee=[];re.forEach((X=>{_.indexOf(X.sniff)>-1&&ee.push(X)}));try{te.default(_).walk((_=>{try{_.dimension=te.default.unit(_.value)}finally{!1===_.dimension&&delete _.dimension}for(let te=0;te<ee.length;te++){const re=ee[te];for(let ee=0;ee<re.matchers.length;ee++){if(p(re.matchers[ee],_))return void X.push(`(${re.property}: ${re.supports})`)}}X.push(...function(_){const X=[];if("function"===_.type&&("conic-gradient"===_.value||"linear-gradient"===_.value||"radial-gradient"===_.value||"repeating-conic-gradient"===_.value||"repeating-linear-gradient"===_.value||"repeating-radial-gradient"===_.value)){let ee=0,te=!1,re=!1;e:for(let ne=0;ne<_.nodes.length;ne++){const ie=_.nodes[ne];if("word"===ie.type&&se.includes(ie.value)&&(te=!0),"div"!==ie.type||","!==ie.value.trim())if("word"!==ie.type||"in"!==ie.value){if("word"!==ie.type&&"function"!==ie.type||ee++,re)switch(_.value){case"conic-gradient":X.push("(background: conic-gradient(in oklch, red 0deg, red 0deg 1deg, red 2deg))");break e;case"linear-gradient":X.push("(background: linear-gradient(in oklch, red 0%, red 0% 1%, red 2%))");break e;case"radial-gradient":X.push("(background: radial-gradient(in oklch, red, red 1px 2px, red 3px))");break e;case"repeating-conic-gradient":X.push("(background: repeating-conic-gradient(in oklch from 0deg, red 0deg, red 0deg 1deg, red 2deg))");break e;case"repeating-linear-gradient":X.push("(background: repeating-linear-gradient(in oklch, red 0%, red 0% 1%, red 2%))");break e;case"repeating-radial-gradient":X.push("(background: repeating-radial-gradient(in oklch, red, red 1px 2px, red 3px))");break e}if(!te&&3===ee)switch(_.value){case"conic-gradient":X.push("(background: conic-gradient(red 0deg, red 0deg 1deg, red 2deg))");break e;case"linear-gradient":X.push("(background: linear-gradient(red 0%, red 0% 1%, red 2%))");break e;case"radial-gradient":X.push("(background: radial-gradient(red, red 1px 2px, red 3px))");break e;case"repeating-conic-gradient":X.push("(background: repeating-conic-gradient(from 0deg, red 0deg, red 0deg 1deg, red 2deg))");break e;case"repeating-linear-gradient":X.push("(background: repeating-linear-gradient(red 0%, red 0% 1%, red 2%))");break e;case"repeating-radial-gradient":X.push("(background: repeating-radial-gradient(red, red 1px 2px, red 3px))");break e}}else re=!0;else ee=0,te=!1}}return X}(_))}))}catch(_){}return Array.from(new Set(X))}const o=()=>({postcssPlugin:"postcss-progressive-custom-properties",RuleExit:(_,{postcss:X})=>{const ee=[],te=new Set;_.each((re=>{if("decl"!==re.type)return;if(!re.variable)return;if("initial"===re.value.trim())return;if(""===re.value.trim())return;if(!te.has(re.prop.toString()))return void te.add(re.prop.toString());const se=i(re.value);if(!se.length)return;const ne=X.atRule({name:"supports",params:se.join(" and "),source:_.source,raws:{before:"\n\n",after:"\n"}}),ie=_.clone();ie.removeAll(),ie.raws.before="\n",ie.append(re.clone()),re.remove(),ne.append(ie),ee.push(ne)})),0!==ee.length&&ee.reverse().forEach((X=>{_.after(X)}))}});o.postcss=!0,_.exports=o},3570:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));const r=_=>{const X=String(Object(_).replaceWith||"[blank]"),ee=te.default().astSync(X),re=Boolean(!("preserve"in Object(_))||_.preserve);return{postcssPlugin:"css-blank-pseudo",Rule:(_,{result:X})=>{if(-1===_.selector.indexOf(":blank"))return;let se;try{const X=te.default((_=>{_.walkPseudos((_=>{":blank"===_.value&&(_.nodes&&_.nodes.length||_.replaceWith(ee.clone()))}))})).processSync(_.selector);se=String(X)}catch(ee){return void _.warn(X,`Failed to parse selector : ${_.selector}`)}if(void 0===se)return;if(se===_.selector)return;const ne=_.clone({selector:se});re?_.before(ne):_.replaceWith(ne)}}};r.postcss=!0,_.exports=r},3318:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));const s=_=>{_="object"==typeof _&&_||re;const X=Boolean(!("preserve"in _)||_.preserve);return{postcssPlugin:"css-has-pseudo",Rule:(_,{result:ee})=>{if(!_.selector.includes(":has("))return;let re;try{const X=te.default((_=>{_.walkPseudos((_=>{if(":has"===_.value&&_.nodes){const X=r(_);_.value=X?":not-has":":has";const ee=te.default.attribute({attribute:o(String(_))});X?_.parent.parent.replaceWith(ee):_.replaceWith(ee)}}))})).processSync(_.selector);re=String(X)}catch(X){return void _.warn(ee,`Failed to parse selector : ${_.selector}`)}void 0!==re&&re!==_.selector&&(X?_.cloneBefore({selector:re}):_.selector=re)}}};s.postcss=!0;const re={preserve:!0},o=_=>{let X="",ee="";const n=()=>{if(ee){const _=encodeURIComponent(ee);let te="",re="";const r=()=>{te&&(re+=te,te="")};let se=!1;for(let X=0;X<_.length;X++){const ee=_[X];if(se)te+=ee,se=!1;else switch(ee){case"%":r(),re+="\\"+ee;continue;case"\\":te+=ee,se=!0;continue;default:te+=ee;continue}}r(),X+=re,ee=""}};let te=!1;for(let re=0;re<_.length;re++){const se=_[re];if(te)ee+=se,te=!1;else switch(se){case":":case"[":case"]":case",":case"(":case")":n(),X+="\\"+se;continue;case"\\":ee+=se,te=!0;continue;default:ee+=se;continue}}return n(),X},r=_=>{var X,ee;return"pseudo"===(null==(X=_.parent)||null==(ee=X.parent)?void 0:ee.type)&&":not"===_.parent.parent.value};_.exports=s},8277:_=>{"use strict";const X=/^media$/i,ee=/\(\s*prefers-color-scheme\s*:\s*(dark|light|no-preference)\s*\)/i,te={dark:48,light:70,"no-preference":22},s=(_,X)=>`(color-index: ${te[X.toLowerCase()]})`,re={dark:48842621,light:70318723,"no-preference":22511989},t=(_,X)=>`(color: ${re[X.toLowerCase()]})`,l=_=>{const te=!("preserve"in Object(_))||_.preserve,re={};return!("mediaQuery"in Object(_))||"color-index"!==_.mediaQuery&&"color"!==_.mediaQuery?(re["color-index"]=!0,re.color=!0):re[_.mediaQuery]=!0,{postcssPlugin:"postcss-prefers-color-scheme",AtRule:_=>{if(!X.test(_.name))return;const{params:se}=_,ne=se.replace(ee,s),ie=se.replace(ee,t);let oe=!1;se!==ne&&re["color-index"]&&(_.cloneBefore({params:ne}),oe=!0),se!==ie&&re.color&&(_.cloneBefore({params:ie}),oe=!0),!te&&oe&&_.remove()}}};l.postcss=!0,_.exports=l},4836:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045));function n(_){const X=_.value;let ee=_.nodes;"rgb"!==X&&"hsl"!==X||(ee=function(_){let X=0;for(let ee=0;ee<_.length;ee++){const te=_[ee];if("div"===te.type&&","===te.value){if(X<2&&(te.value=" ",te.type="space"),2===X&&(te.value="/"),X>2)return;X++}}return _}(ee));const re=ee.slice().filter((_=>"comment"!==_.type&&"space"!==_.type));let se=null;if("hsl"===X||"hsla"===X?se=function(_){if(!function(_){if(!_||"word"!==_.type)return!1;if(!o(_))return!1;const X=te.default.unit(_.value);if(!X)return!1;return!!X.number&&("deg"===X.unit||"grad"===X.unit||"rad"===X.unit||"turn"===X.unit||""===X.unit)}(_[0]))return null;if(!t(_[1]))return null;if(!t(_[2]))return null;const X={h:te.default.unit(_[0].value),hNode:_[0],s:te.default.unit(_[1].value),sNode:_[1],l:te.default.unit(_[2].value),lNode:_[2]};if(function(_){switch(_.unit){case"deg":return void(_.unit="");case"rad":return _.unit="",void(_.number=Math.round(180*parseFloat(_.number)/Math.PI).toString());case"grad":return _.unit="",void(_.number=Math.round(.9*parseFloat(_.number)).toString());case"turn":_.unit="",_.number=Math.round(360*parseFloat(_.number)).toString()}}(X.h),""!==X.h.unit)return null;X.hNode.value=X.h.number,l(_[3])&&(X.slash=_[3]);(t(_[4])||u(_[4])||a(_[4]))&&(X.alpha=_[4]);return X}(re):"rgb"!==X&&"rgba"!==X||(se=function(_){if(!t(_[0]))return null;if(!t(_[1]))return null;if(!t(_[2]))return null;const X={r:te.default.unit(_[0].value),rNode:_[0],g:te.default.unit(_[1].value),gNode:_[1],b:te.default.unit(_[2].value),bNode:_[2]};"%"===X.r.unit&&(X.r.number=String(Math.floor(Number(X.r.number)/100*255)),X.rNode.value=X.r.number);"%"===X.g.unit&&(X.g.number=String(Math.floor(Number(X.g.number)/100*255)),X.gNode.value=X.g.number);"%"===X.b.unit&&(X.b.number=String(Math.floor(Number(X.b.number)/100*255)),X.bNode.value=X.b.number);l(_[3])&&(X.slash=_[3]);(t(_[4])||u(_[4])||a(_[4]))&&(X.alpha=_[4]);return X}(re)),!se)return;if(re.length>3&&(!se.slash||!se.alpha))return;!function(_,X,ee){"hsl"===_.value||"hsla"===_.value?_.value="hsl":"rgb"!==_.value&&"rgba"!==_.value||(_.value="rgb");if(!X||!ee)return;"hsl"===_.value?_.value="hsla":_.value="rgba";if(X.value=",",X.before="",!function(_){if(!_||"word"!==_.type)return!1;if(!o(_))return!1;const X=te.default.unit(_.value);if(!X)return!1;return!!X.number}(ee))return;const re=te.default.unit(ee.value);if(!re)return;"%"===re.unit&&(re.number=String(parseFloat(re.number)/100),ee.value=String(re.number))}(_,se.slash,se.alpha);const[ne,ie]=function(_){if(function(_){if(void 0!==_.r)return!0;return!1}(_))return[_.rNode,_.gNode,_.bNode];return[_.hNode,_.sNode,_.lNode]}(se);_.nodes.splice(_.nodes.indexOf(ne)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),_.nodes.splice(_.nodes.indexOf(ie)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""})}function t(_){if(!_||"word"!==_.type)return!1;if(!o(_))return!1;const X=te.default.unit(_.value);return!!X&&("%"===X.unit||""===X.unit)}function u(_){return _&&"function"===_.type&&"calc"===_.value}function a(_){return _&&"function"===_.type&&"var"===_.value}function l(_){return _&&"div"===_.type&&"/"===_.value}function o(_){if(!_||!_.value)return!1;try{return!1!==te.default.unit(_.value)}catch(_){return!1}}const i=_=>{const X="preserve"in Object(_)&&Boolean(_.preserve);return{postcssPlugin:"postcss-color-functional-notation",Declaration:(_,{result:ee,postcss:re})=>{if(X&&function(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("supports"===X.name&&-1!==X.params.indexOf("(color: rgb(0 0 0 / 0.5)) and (color: hsl(0 0% 0% / 0.5))"))return!0;X=X.parent}else X=X.parent;return!1}(_))return;const se=_.value;if(!/(^|[^\w-])(hsla?|rgba?)\(/i.test(se))return;let ne;try{ne=te.default(se)}catch(X){_.warn(ee,`Failed to parse value '${se}' as a hsl or rgb function. Leaving the original value intact.`)}if(void 0===ne)return;ne.walk((_=>{_.type&&"function"===_.type&&("hsl"!==_.value&&"hsla"!==_.value&&"rgb"!==_.value&&"rgba"!==_.value||n(_))}));const ie=String(ne);if(ie!==se)if(X&&_.variable){const X=_.parent,ee="(color: rgb(0 0 0 / 0.5)) and (color: hsl(0 0% 0% / 0.5))",te=re.atRule({name:"supports",params:ee,source:_.source}),se=X.clone();se.removeAll(),se.append(_.clone()),te.append(se);let ne=X,oe=X.next();for(;ne&&oe&&"atrule"===oe.type&&"supports"===oe.name&&oe.params===ee;)ne=oe,oe=oe.next();ne.after(te),_.value=ie}else X?_.cloneBefore({value:ie}):_.value=ie}}};i.postcss=!0,_.exports=i},2060:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045)),re={preserve:!1};const r=_=>{if(o(_)){const{value:X}=_,ee=te.default(X);ee.walk((_=>{c(_)&&n(_)}));const se=ee.toString();se!==X&&(re.preserve?_.cloneBefore({value:se}):_.value=se)}},se=/^#([0-9A-Fa-f]{4}(?:[0-9A-Fa-f]{4})?)$/,ne=/#([0-9A-Fa-f]{4}(?:[0-9A-Fa-f]{4})?)\b/,o=_=>ne.test(_.value),c=_=>"word"===_.type&&se.test(_.value),n=_=>{const X=_.value,ee=`0x${5===X.length?X.slice(1).replace(/[0-9A-f]/g,"$&$&"):X.slice(1)}`,[te,re,se,ne]=[parseInt(ee.slice(2,4),16),parseInt(ee.slice(4,6),16),parseInt(ee.slice(6,8),16),Math.round(parseInt(ee.slice(8,10),16)/255*1e5)/1e5];_.value=`rgba(${te},${re},${se},${ne})`};function u(_){return re.preserve="preserve"in Object(_)&&Boolean(_.preserve),{postcssPlugin:"postcss-color-hex-alpha",Declaration:r}}u.postcss=!0,_.exports=u},7106:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045));const re=Function.bind.bind(RegExp.prototype.test)(/^rebeccapurple$/i);var se={preserve:!1};const s=_=>{const{value:X}=_;if(ne(X)){const ee=te.default(X);ee.walk((_=>{"word"===_.type&&(_=>{re(_.value)&&(_.value="#639")})(_)}));const ne=String(ee);ne!==X&&(se.preserve?_.cloneBefore({value:ne}):_.value=ne)}},ne=Function.bind.bind(RegExp.prototype.test)(/(^|[^\w-])rebeccapurple([^\w-]|$)/i);function c(_){return se.preserve="preserve"in Object(_)&&Boolean(_.preserve),{postcssPlugin:"postcss-color-rebeccapurple",Declaration:s}}c.postcss=!0,_.exports=c},8806:(_,X,ee)=>{"use strict";var te=ee(2045),re=ee(1017),se=ee(7310),ne=ee(977),ie=ee(7147);function n(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}function i(_){if(_&&_.__esModule)return _;var X=Object.create(null);return _&&Object.keys(_).forEach((function(ee){if("default"!==ee){var te=Object.getOwnPropertyDescriptor(_,ee);Object.defineProperty(X,ee,te.get?te:{enumerable:!0,get:function(){return _[ee]}})}})),X.default=_,Object.freeze(X)}var oe=n(te),ae=n(re);function u(_){const X=_.selector?_:_.parent;return/(!\s*)?postcss-custom-properties:\s*off\b/i.test(X.toString())}function l(_,X){const ee=new Map,te=new Map,re=new Map;_.nodes.slice().forEach((_=>{const re=m(_)?ee:w(_)?te:null;re&&(_.nodes.slice().forEach((_=>{if(_.variable&&!u(_)){const{prop:ee}=_;re.set(ee,oe.default(_.value)),X.preserve||_.remove()}})),X.preserve||!d(_)||u(_)||_.remove())}));for(const[_,X]of ee.entries())re.set(_,X);for(const[_,X]of te.entries())re.set(_,X);return re}const le=/^html$/i,ue=/^:root$/i,m=_=>"rule"===_.type&&_.selector.split(",").some((_=>le.test(_)))&&Object(_.nodes).length,w=_=>"rule"===_.type&&_.selector.split(",").some((_=>ue.test(_)))&&Object(_.nodes).length,d=_=>0===Object(_.nodes).length;function v(_){const X=new Map;if("customProperties"in _)for(const[ee,te]of Object.entries(_.customProperties))X.set(ee,oe.default(te.toString()));if("custom-properties"in _)for(const[ee,te]of Object.entries(_["custom-properties"]))X.set(ee,oe.default(te.toString()));return X}async function y(_){let X;try{X=await(te=_,Promise.resolve().then((function(){return i(ee(5829)(te))})))}catch(te){X=await function(_){return Promise.resolve().then((function(){return i(ee(5829)(_))}))}(se.pathToFileURL(_).href)}var te;return v("default"in X?X.default:X)}async function h(_){const X=(await Promise.all(_.map((async _=>{if(_ instanceof Promise?_=await _:_ instanceof Function&&(_=await _()),"string"==typeof _){const X=ae.default.resolve(_);return{type:ae.default.extname(X).slice(1).toLowerCase(),from:X}}if("customProperties"in _&&Object(_.customProperties)===_.customProperties)return _;if("custom-properties"in _&&Object(_["custom-properties"])===_["custom-properties"])return _;if("from"in _){const X=ae.default.resolve(_.from);let ee=_.type;return ee||(ee=ae.default.extname(X).slice(1).toLowerCase()),{type:ee,from:X}}return Object.keys(_).length,null})))).filter((_=>!!_)),ee=await Promise.all(X.map((async _=>{if("type"in _&&"from"in _){if("css"===_.type||"pcss"===_.type)return await async function(_){const X=await ie.promises.readFile(_);return l(ne.parse(X,{from:_.toString()}),{preserve:!0})}(_.from);if("js"===_.type||"cjs"===_.type)return await y(_.from);if("mjs"===_.type)return await y(_.from);if("json"===_.type)return await async function(_){return v(await g(_))}(_.from);throw new Error("Invalid source type: "+_.type)}return v(_)}))),te=new Map;return ee.forEach((_=>{for(const[X,ee]of _.entries())te.set(X,ee)})),te}const g=async _=>JSON.parse((await ie.promises.readFile(_)).toString());function j(_,X){return _.nodes&&_.nodes.length&&_.nodes.slice().forEach((ee=>{if(O(ee)){const[te,...re]=ee.nodes.filter((_=>"div"!==_.type)),{value:se}=te,ne=_.nodes.indexOf(ee);if(X.has(se)){const ee=X.get(se).nodes;!function(_,X,ee){const te=new Map(X);te.delete(ee),j(_,te)}({nodes:ee},X,se),ne>-1&&_.nodes.splice(ne,1,...ee)}else re.length&&(ne>-1&&_.nodes.splice(ne,1,...ee.nodes.slice(ee.nodes.indexOf(re[0]))),j(_,X))}else j(ee,X)})),_.toString()}const ce=/^var$/i,O=_=>"function"===_.type&&ce.test(_.value)&&Object(_.nodes).length>0;var $=(_,X,ee)=>{if(F(_)&&!function(_){const X=_.prev();return Boolean(u(_)||X&&"comment"===X.type&&/(!\s*)?postcss-custom-properties:\s*ignore\s+next\b/i.test(X.text))}(_)){const te=_.value;let re=j(oe.default(te),X);const se=new Set;for(;fe.test(re)&&!se.has(re);){se.add(re);re=j(oe.default(re),X)}if(re!==te)if(ee.preserve){const X=_.cloneBefore({value:re});S(X)&&(X.raws.value.value=X.value.replace(de,"$1"),X.raws.value.raw=X.raws.value.value+X.raws.value.raw.replace(de,"$2"))}else _.value=re,S(_)&&(_.raws.value.value=_.value.replace(de,"$1"),_.raws.value.raw=_.raws.value.value+_.raws.value.raw.replace(de,"$2"))}};const pe=/^--[A-z][\w-]*$/,fe=/(^|[^\w-])var\([\W\w]+\)/,F=_=>!pe.test(_.prop)&&fe.test(_.value),S=_=>"value"in Object(Object(_.raws).value)&&"raw"in _.raws.value&&de.test(_.raws.value.raw),de=/^([\W\w]+)(\s*\/\*[\W\w]+?\*\/)$/;async function E(_,X,ee){"css"===X&&await async function(_,X){const ee=`:root {\n${Object.keys(X).reduce(((_,ee)=>(_.push(`\t${ee}: ${X[ee]};`),_)),[]).join("\n")}\n}\n`;await ie.promises.writeFile(_,ee)}(_,ee),"scss"===X&&await async function(_,X){const ee=`${Object.keys(X).reduce(((_,ee)=>{const te=ee.replace("--","$");return _.push(`${te}: ${X[ee]};`),_}),[]).join("\n")}\n`;await ie.promises.writeFile(_,ee)}(_,ee),"js"===X&&await async function(_,X){const ee=`module.exports = {\n\tcustomProperties: {\n${Object.keys(X).reduce(((_,ee)=>(_.push(`\t\t'${q(ee)}': '${q(X[ee])}'`),_)),[]).join(",\n")}\n\t}\n};\n`;await ie.promises.writeFile(_,ee)}(_,ee),"json"===X&&await async function(_,X){const ee=`${JSON.stringify({"custom-properties":X},null,"  ")}\n`;await ie.promises.writeFile(_,ee)}(_,ee),"mjs"===X&&await async function(_,X){const ee=`export const customProperties = {\n${Object.keys(X).reduce(((_,ee)=>(_.push(`\t'${q(ee)}': '${q(X[ee])}'`),_)),[]).join(",\n")}\n};\n`;await ie.promises.writeFile(_,ee)}(_,ee)}function M(_){const X={};for(const[ee,te]of _.entries())X[ee]=te.toString();return X}const q=_=>_.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r"),N=_=>{const X=!("preserve"in Object(_))||Boolean(_.preserve),ee="overrideImportFromWithRoot"in Object(_)&&Boolean(_.overrideImportFromWithRoot),te="disableDeprecationNotice"in Object(_)&&Boolean(_.disableDeprecationNotice);let re=[];Array.isArray(null==_?void 0:_.importFrom)?re=_.importFrom:null!=_&&_.importFrom&&(re=[_.importFrom]);let se=[];Array.isArray(null==_?void 0:_.exportTo)?se=_.exportTo:null!=_&&_.exportTo&&(se=[_.exportTo]);const ne=h(re),ie=0===re.length&&0===se.length;return{postcssPlugin:"postcss-custom-properties",prepare(){let _=new Map;return ie?{Once:ee=>{_=l(ee,{preserve:X})},Declaration:ee=>{$(ee,_,{preserve:X})},OnceExit:()=>{_.clear()}}:{Once:async te=>{const re=(await ne).entries(),ie=l(te,{preserve:X}).entries();if(ee)for(const[X,ee]of[...re,...ie])_.set(X,ee);else for(const[X,ee]of[...ie,...re])_.set(X,ee);await function(_,X){return Promise.all(X.map((async X=>{if(X instanceof Function)return void await X(M(_));if("string"==typeof X){const ee=ae.default.resolve(X),te=ae.default.extname(ee).slice(1).toLowerCase();return void await E(ee,te,M(_))}let ee={};if(ee="toJSON"in X?X.toJSON(M(_)):M(_),"to"in X){const _=ae.default.resolve(X.to);let te=X.type;return te||(te=ae.default.extname(_).slice(1).toLowerCase()),void await E(_,te,ee)}"customProperties"in X?X.customProperties=ee:"custom-properties"in X&&(X["custom-properties"]=ee)})))}(_,se)},Declaration:ee=>{$(ee,_,{preserve:X})},OnceExit:(X,{result:ee})=>{!te&&(re.length>0||se.length>0)&&X.warn(ee,'"importFrom" and "exportTo" will be removed in a future version of postcss-custom-properties.\nWe are looking for insights and anecdotes on how these features are used so that we can design the best alternative.\nPlease let us know if our proposal will work for you.\nVisit the discussion on github for more details. https://github.com/csstools/postcss-plugins/discussions/192'),_.clear()}}}}};N.postcss=!0,_.exports=N},50:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));const re=/:dir\([^)]*\)/;function o(_){const X=Object(_).dir,ee=Boolean(Object(_).preserve),se=Boolean(Object(_).shadow);return{postcssPlugin:"postcss-dir-pseudo-class",Rule(_,{result:ne}){let ie,oe=!1;if(re.test(_.selector)){try{ie=te.default((ee=>{ee.nodes.forEach((ee=>{ee.walk((ee=>{if("pseudo"!==ee.type)return;if(":dir"!==ee.value)return;const re=ee.nodes.toString();if("rtl"!==re&&"ltr"!==re)return;const ie=ee.parent;ie.nodes.filter((_=>"pseudo"===_.type&&":dir"===_.value)).length>1&&!oe&&(oe=!0,_.warn(ne,`Hierarchical :dir pseudo class usage can't be transformed correctly to [dir] attributes. This will lead to incorrect selectors for "${_.selector}"`));const ae=ee.prev(),le=ee.next(),ue=ae&&ae.type&&"combinator"!==ae.type,ce=le&&le.type&&"combinator"!==le.type,pe=le&&le.type&&("combinator"!==le.type||"combinator"===le.type&&" "===le.value);ue||ce||0===ie.nodes.indexOf(ee)&&pe||1===ie.nodes.length?ee.remove():ee.replaceWith(te.default.universal());const fe=ie.nodes[0],de=fe&&"combinator"===fe.type&&" "===fe.value,he=fe&&"tag"===fe.type&&"html"===fe.value,me=fe&&"pseudo"===fe.type&&":root"===fe.value;!fe||he||me||de||ie.prepend(te.default.combinator({value:" "}));const ge=X===re,be=te.default.attribute({attribute:"dir",operator:"=",quoteMark:'"',value:`"${re}"`}),ve=te.default.pseudo({value:":host-context"});ve.append(be);const ye=te.default.pseudo({value:(he||me?"":"html")+":not"});ye.append(te.default.attribute({attribute:"dir",operator:"=",quoteMark:'"',value:`"${"ltr"===re?"rtl":"ltr"}"`})),ge?he?ie.insertAfter(fe,ye):ie.prepend(ye):he?ie.insertAfter(fe,be):se&&!me?ie.prepend(ve):ie.prepend(be)}))}))})).processSync(_.selector)}catch(X){return void _.warn(ne,`Failed to parse selector : ${_.selector}`)}void 0!==ie&&ie!==_.selector&&(ee?_.cloneBefore({selector:ie}):_.selector=ie)}}}}o.postcss=!0,_.exports=o},1426:(_,X,ee)=>{"use strict";var te=ee(5449),re=ee(2045);function r(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var se=r(te),ne=r(re);function s(_){return _.includes("conic-gradient(")||_.includes("linear-gradient(")||_.includes("radial-gradient(")||_.includes("repeating-conic-gradient(")||_.includes("repeating-linear-gradient(")||_.includes("repeating-radial-gradient(")}const ie=["at","bottom","center","circle","closest-corner","closest-side","ellipse","farthest-corner","farthest-side","from","in","left","right","to","top"],o=_=>"div"===_.type&&","===_.value;function l(_){try{return!1!==ne.default.unit(null==_?void 0:_.value)}catch(_){return!1}}const c=_=>({postcssPlugin:"postcss-double-position-gradients",Declaration(X,{result:ee}){if(!s(X.value))return;if(function(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("supports"===X.name&&s(X.params))return!0;X=X.parent}else X=X.parent;return!1}(X))return;let te;try{te=ne.default(X.value)}catch(_){X.warn(ee,`Failed to parse value '${X.value}' as a CSS gradient. Leaving the original value intact.`)}if(void 0===te)return;te.walk((_=>{if("function"!==_.type||"conic-gradient"!==(X=_.value)&&"linear-gradient"!==X&&"radial-gradient"!==X&&"repeating-conic-gradient"!==X&&"repeating-linear-gradient"!==X&&"repeating-radial-gradient"!==X)return;var X;const ee=_.nodes.filter((_=>"comment"!==_.type&&"space"!==_.type));let te=!1;ee.forEach(((X,ee,re)=>{if("word"===X.type&&ie.includes(X.value)&&(te=!0),"div"===X.type&&","===X.value&&(te=!1),te)return;const se=Object(re[ee-1]),ne=Object(re[ee-2]),oe=Object(re[ee+1]);if(ne.type&&l(se)&&l(X)){const ee=ne,te={type:"div",value:",",before:o(oe)?oe.before:"",after:o(oe)?"":" "};_.nodes.splice(_.nodes.indexOf(X)-1,0,te,ee)}}))}));const re=te.toString();if(re!==X.value){if(_.preserve)return void X.cloneBefore({value:re});X.value=re}}});c.postcss=!0;const u=_=>{const X=Object.assign({enableProgressiveCustomProperties:!0,preserve:!0},_);return X.enableProgressiveCustomProperties&&X.preserve?{postcssPlugin:"postcss-double-position-gradients",plugins:[se.default(),c(X)]}:c(X)};u.postcss=!0,_.exports=u},3365:(_,X,ee)=>{"use strict";var te=ee(2045),re=ee(7147),se=ee(1017);function a(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}function r(_){if(_&&_.__esModule)return _;var X=Object.create(null);return _&&Object.keys(_).forEach((function(ee){if("default"!==ee){var te=Object.getOwnPropertyDescriptor(_,ee);Object.defineProperty(X,ee,te.get?te:{enumerable:!0,get:function(){return _[ee]}})}})),X.default=_,Object.freeze(X)}var ne=a(te),ie=a(re),oe=a(se),c=(_,X)=>{const ee=ne.default(_);return ee.walk((_=>{if((_=>_&&"function"===_.type&&"env"===_.value)(_)){const[ee]=_.nodes;"word"===ee.type&&void 0!==X[ee.value]&&(_.nodes=[],_.type="word",_.value=X[ee.value])}})),ee.toString()};function u(_){return Object.assign({},Object(_).environmentVariables||Object(_)["environment-variables"])}function l(_){return _.map((_=>{if(_ instanceof Promise)return _;if(_ instanceof Function)return _();const X=_===Object(_)?_:{from:String(_)};if(X.environmentVariables||X["environment-variables"])return X;const ee=String(X.from||"");return{type:(X.type||oe.default.extname(ee).slice(1)).toLowerCase(),from:ee}})).reduce((async(_,X)=>{const{type:te,from:re}=await X;return"js"===te||"cjs"===te?Object.assign(_,await async function(_){var X;return u(await(X=oe.default.resolve(_),Promise.resolve().then((function(){return r(ee(1736)(X))}))))}(re)):"json"===te?Object.assign(_,await async function(_){return u(await f(oe.default.resolve(_)))}(re)):Object.assign(_,u(await X))}),{})}const f=async _=>JSON.parse(await(_=>new Promise(((X,ee)=>{ie.default.readFile(_,"utf8",((_,te)=>{_?ee(_):X(te)}))})))(_));function d(_){const X=l([].concat(Object(_).importFrom||[])),ee="disableDeprecationNotice"in Object(_)&&Boolean(_.disableDeprecationNotice);let te=!1;return{postcssPlugin:"postcss-env-fn",async AtRule(_,{result:re}){let se;try{se=c(_.params,await X)}catch(X){_.warn(re,`Failed to parse params '${_.params}' as an environment value. Leaving the original value intact.`)}void 0!==se&&se!==_.params&&(_.params=se,ee||te||(te=!0,_.warn(re,"postcss-env-function is deprecated and will be removed.\nCheck the discussion on github for more details. https://github.com/csstools/postcss-plugins/discussions/192")))},async Declaration(_,{result:re}){let se;try{se=c(_.value,await X)}catch(X){_.warn(re,`Failed to parse value '${_.value}' as an environment value. Leaving the original value intact.`)}void 0!==se&&se!==_.value&&(_.value=se,ee||te||(te=!0,_.warn(re,"postcss-env-function is deprecated and will be removed.\nWe are looking for insights and anecdotes on how these features are used so that we can design the best alternative.\nPlease let us know if our proposal will work for you.\nVisit the discussion on github for more details. https://github.com/csstools/postcss-plugins/discussions/192")))}}}d.postcss=!0,_.exports=d},3073:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));const t=_=>{_=Object(_);const X=Boolean(!("preserve"in _)||_.preserve),ee=String(_.replaceWith||".focus-visible"),re=te.default().astSync(ee);return{postcssPlugin:"postcss-focus-visible",Rule(_,{result:ee}){if(!_.selector.includes(":focus-visible"))return;let se;try{const X=te.default((_=>{_.walkPseudos((_=>{":focus-visible"===_.value&&(_.nodes&&_.nodes.length||_.replaceWith(re.clone({})))}))})).processSync(_.selector);se=String(X)}catch(X){return void _.warn(ee,`Failed to parse selector : ${_.selector}`)}if(void 0===se)return;if(se===_.selector)return;const ne=_.clone({selector:se});X?_.before(ne):_.replaceWith(ne)}}};t.postcss=!0,_.exports=t},8742:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));const s=_=>{const X=String(Object(_).replaceWith||"[focus-within]"),ee=Boolean(!("preserve"in Object(_))||_.preserve),re=te.default().astSync(X);return{postcssPlugin:"postcss-focus-within",Rule:(_,{result:X})=>{if(!_.selector.includes(":focus-within"))return;let se;try{const X=te.default((_=>{_.walkPseudos((_=>{":focus-within"===_.value&&(_.nodes&&_.nodes.length||_.replaceWith(re.clone({})))}))})).processSync(_.selector);se=String(X)}catch(ee){return void _.warn(X,`Failed to parse selector : ${_.selector}`)}if(void 0===se)return;if(se===_.selector)return;const ne=_.clone({selector:se});ee?_.before(ne):_.replaceWith(ne)}}};s.postcss=!0,_.exports=s},9060:_=>{"use strict";const X=/^(column-gap|gap|row-gap)$/i,o=_=>"display"===_.prop&&"grid"===_.value;function p(_){const ee=!("preserve"in Object(_))||Boolean(_.preserve);return{postcssPlugin:"postcss-gap-properties",Declaration(_){X.test(_.prop)&&_.parent.some(o)&&(_.cloneBefore({prop:`grid-${_.prop}`}),ee||_.remove())}}}p.postcss=!0,_.exports=p},6008:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045));const re=/^(cross-fade|image|(repeating-)?(conic|linear|radial)-gradient|url|var)$/i;function r(_){return!(!_||!_.type)&&("string"===_.type?"url("+te.default.stringify(_)+")":!("function"!==_.type||!re.test(_.value))&&te.default.stringify(_))}const se={dpcm:2.54,dpi:1,dppx:96,x:96};function a(_,X,ee){if("boolean"==typeof _)return!1;const te=Math.floor(_/se.x*100)/100;return X.atRule({name:"media",params:`(-webkit-min-device-pixel-ratio: ${te}), (min-resolution: ${_}dpi)`,source:ee.source})}function o(_){if(!_)return!1;if("word"!==_.type)return!1;if(!function(_){if(!_||!_.value)return!1;try{return!1!==te.default.unit(_.value)}catch(_){return!1}}(_))return!1;const X=te.default.unit(_.value);return!!X&&(X.unit.toLowerCase()in se&&Number(X.number)*se[X.unit.toLowerCase()])}const s=(_,X,ee)=>{if("warn"===_.oninvalid)_.decl.warn(_.result,X,{word:String(ee)});else if("throw"===_.oninvalid)throw _.decl.error(X,{word:String(ee)})},ne=/(^|[^\w-])(-webkit-)?image-set\(/i,ie=/^(-webkit-)?image-set$/i,c=_=>{const X=!("preserve"in Object(_))||Boolean(_.preserve),ee="oninvalid"in Object(_)?_.oninvalid:"ignore";return{postcssPlugin:"postcss-image-set-function",Declaration(_,{result:re,postcss:se}){const oe=_.value;if(!ne.test(oe))return;let ae;try{ae=te.default(oe)}catch(X){_.warn(re,`Failed to parse value '${oe}' as an image-set function. Leaving the original value intact.`)}if(void 0===ae)return;const le=[];ae.walk((X=>{if("function"!==X.type)return;if(!ie.test(X.value))return;let se=!1;if(te.default.walk(X.nodes,(_=>{"function"===_.type&&ie.test(_.value)&&(se=!0)})),se)return s({decl:_,oninvalid:ee,result:re},"nested image-set functions are not allowed",te.default.stringify(X)),!1;const ne=X.nodes.filter((_=>"comment"!==_.type&&"space"!==_.type));le.push({imageSetFunction:X,imageSetOptionNodes:ne})})),((_,X,ee)=>{const re=X.parent,se=new Map,ne=X.value;for(let re=0;re<_.length;re++){const{imageSetFunction:oe,imageSetOptionNodes:ae}=_[re],le=new Map,ue=ae.length;let ce=-1;for(;ce<ue;){const _=ce<0||(ie=ae[ce],"div"===Object(ie).type&&","===Object(ie).value),re=r(ae[ce+1]),ue=o(ae[ce+2]),pe=a(ue,ee.postcss,X);if(!_)return void s(ee,"expected a comma",te.default.stringify(ae));if(!re)return void s(ee,"unexpected image",te.default.stringify(ae));if(!pe||!ue||le.has(ue))return void s(ee,"unexpected resolution",te.default.stringify(ae));if(le.set(ue,pe),se.has(ue)){const _=se.get(ue);_.value=_.value.replace(te.default.stringify(oe),re.trim()),se.set(ue,_)}else se.set(ue,{atRule:pe,value:ne.replace(te.default.stringify(oe),re.trim())});ce+=3}}var ie;for(const{atRule:_,value:ee}of se.values()){const te=re.clone().removeAll(),se=X.clone({value:ee});te.append(se),_.append(te)}const oe=Array.from(se.keys()).sort(((_,X)=>_-X)).map((_=>se.get(_).atRule));if(!oe.length)return;const ae=oe[0],le=oe.slice(1);le.length&&re.after(le);const ue=ae.nodes[0].nodes[0];X.cloneBefore({value:ue.value.trim()}),ee.preserve||(X.remove(),re.nodes.length||re.remove())})(le,_,{decl:_,oninvalid:ee,preserve:X,result:re,postcss:se})}}};c.postcss=!0,_.exports=c},6157:(_,X,ee)=>{"use strict";var te=ee(5449),re=ee(2045);function n(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var se=n(te),ne=n(re);
/**
 * Simple matrix (and vector) multiplication
 * Warning: No error handling for incompatible dimensions!
 * <AUTHOR> Verou 2020 MIT License
 *
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js
 */function a(_,X){const ee=_.length;let te,re;te=Array.isArray(_[0])?_:[_],Array.isArray(X[0])||(re=X.map((_=>[_])));const se=re[0].length,ne=re[0].map(((_,X)=>re.map((_=>_[X]))));let ie=te.map((_=>ne.map((X=>Array.isArray(_)?_.reduce(((_,ee,te)=>_+ee*(X[te]||0)),0):X.reduce(((X,ee)=>X+ee*_),0)))));return 1===ee&&(ie=ie[0]),1===se?ie.map((_=>_[0])):ie}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */const ie=[.3457/.3585,1,.2958/.3585];function i(_){return _.map((function(_){const X=_<0?-1:1,ee=Math.abs(_);return ee<.04045?_/12.92:X*Math.pow((ee+.055)/1.055,2.4)}))}function s(_){return _.map((function(_){const X=_<0?-1:1,ee=Math.abs(_);return ee>.0031308?X*(1.055*Math.pow(ee,1/2.4)-.055):12.92*_}))}function l(_){return a([[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],_)}function c(_){return a([[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]],_)}function f(_){return i(_)}function p(_){return s(_)}function d(_){return a([[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],_)}function h(_){return a([[2.493496911941425,-.9313836179191239,-.40271078445071684],[-.8294889695615747,1.7626640603183463,.023624685841943577],[.03584583024378447,-.07617238926804182,.9568845240076872]],_)}function m(_){return a([[.9554734527042182,-.023098536874261423,.0632593086610217],[-.028369706963208136,1.0099954580058226,.021041398966943008],[.012314001688319899,-.020507696433477912,1.3303659366080753]],_)}function v(_){const X=24389/27,ee=216/24389,te=[];te[1]=(_[0]+16)/116,te[0]=_[1]/500+te[1],te[2]=te[1]-_[2]/200;return[Math.pow(te[0],3)>ee?Math.pow(te[0],3):(116*te[0]-16)/X,_[0]>8?Math.pow((_[0]+16)/116,3):_[0]/X,Math.pow(te[2],3)>ee?Math.pow(te[2],3):(116*te[2]-16)/X].map(((_,X)=>_*ie[X]))}function b(_){return[_[0],_[1]*Math.cos(_[2]*Math.PI/180),_[1]*Math.sin(_[2]*Math.PI/180)]}function y(_){const X=a([[.8190224432164319,.3619062562801221,-.12887378261216414],[.0329836671980271,.9292868468965546,.03614466816999844],[.048177199566046255,.26423952494422764,.6335478258136937]],_);return a([[.2104542553,.793617785,-.0040720468],[1.9779984951,-2.428592205,.4505937099],[.0259040371,.7827717662,-.808675766]],X.map((_=>Math.cbrt(_))))}function M(_){const X=a([[.9999999984505198,.39633779217376786,.2158037580607588],[1.0000000088817609,-.10556134232365635,-.06385417477170591],[1.0000000546724108,-.08948418209496575,-1.2914855378640917]],_);return a([[1.2268798733741557,-.5578149965554813,.28139105017721583],[-.04057576262431372,1.1122868293970594,-.07171106666151701],[-.07637294974672142,-.4214933239627914,1.5869240244272418]],X.map((_=>_**3)))}function g(_){const X=180*Math.atan2(_[2],_[1])/Math.PI;return[_[0],Math.sqrt(_[1]**2+_[2]**2),X>=0?X:X+360]}function x(_){return[_[0],_[1]*Math.cos(_[2]*Math.PI/180),_[1]*Math.sin(_[2]*Math.PI/180)]}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js
 */function F(_,X){const[ee,te,re]=_,[se,ne,ie]=X,oe=ee-se,ae=te-ne,le=re-ie;return Math.sqrt(oe**2+ae**2+le**2)}function P(_,X,ee){return function(_,X,ee){let te=0,re=_[1];const se=_;for(;re-te>1e-4;){const _=w(X(se));F(x(se),x(ee(_)))-.02<1e-4?te=se[1]:re=se[1],se[1]=(re+te)/2}return w(X([...se]))}(_,X,ee)}function w(_){return _.map((_=>_<0?0:_>1?1:_))}function I(_){const[X,ee,te]=_;return X>=-1e-4&&X<=1.0001&&ee>=-1e-4&&ee<=1.0001&&te>=-1e-4&&te<=1.0001}function N(_){const[X,ee,te]=_;let re=[Math.max(X,0),Math.min(Math.max(ee,-160),160),Math.min(Math.max(te,-160),160)];re=v(re);let se=re.slice();return se=m(se),se=y(se),se=g(se),se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),re=m(re),re=h(re),re=p(re),I(re)?[w(re),!0]:[P(se,(_=>p(_=h(_=M(_=x(_))))),(_=>g(_=y(_=d(_=f(_)))))),!1]}function S(_){const[X,ee,te]=_;let re=[Math.max(X,0),Math.min(Math.max(ee,-160),160),Math.min(Math.max(te,-160),160)];re=v(re);let se=re.slice();return se=m(se),se=y(se),se=g(se),se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),re=m(re),re=c(re),re=s(re),I(re)?w(re).map((_=>Math.round(255*_))):P(se,(_=>s(_=c(_=M(_=x(_))))),(_=>g(_=y(_=l(_=i(_)))))).map((_=>Math.round(255*_)))}function O(_){const[X,ee,te]=_;let re=[Math.max(X,0),ee,te%360];re=b(re),re=v(re);let se=re.slice();return se=m(se),se=y(se),se=g(se),se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),re=m(re),re=h(re),re=p(re),I(re)?[w(re),!0]:[P(se,(_=>p(_=h(_=M(_=x(_))))),(_=>g(_=y(_=d(_=f(_)))))),!1]}function A(_){const[X,ee,te]=_;let re=[Math.max(X,0),ee,te%360];re=b(re),re=v(re);let se=re.slice();return se=m(se),se=y(se),se=g(se),se[0]<1e-6&&(se=[0,0,0]),se[0]>.999999&&(se=[1,0,0]),re=m(re),re=c(re),re=s(re),I(re)?w(re).map((_=>Math.round(255*_))):P(se,(_=>s(_=c(_=M(_=x(_))))),(_=>g(_=y(_=l(_=i(_)))))).map((_=>Math.round(255*_)))}function q(_){const X=_.value,ee=_.nodes.slice().filter((_=>"comment"!==_.type&&"space"!==_.type));let te=null;if("lab"===X?te=$(ee):"lch"===X&&(te=C(ee)),!te)return;_.value="rgb",function(_,X,ee){if(!X||!ee)return;if(_.value="rgba",X.value=",",X.before="",!function(_){if(!_||"word"!==_.type)return!1;if(!H(_))return!1;const X=ne.default.unit(_.value);if(!X)return!1;return!!X.number}(ee))return;const te=ne.default.unit(ee.value);if(!te)return;"%"===te.unit&&(te.number=String(parseFloat(te.number)/100),ee.value=String(te.number))}(_,te.slash,te.alpha);const[re,se,ie]=G(te),[oe,ae,le]=L(te),ue=("lab"===X?S:A)([oe.number,ae.number,le.number].map((_=>parseFloat(_))));_.nodes.splice(_.nodes.indexOf(re)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),_.nodes.splice(_.nodes.indexOf(se)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),z(_.nodes,re,{...re,value:String(ue[0])}),z(_.nodes,se,{...se,value:String(ue[1])}),z(_.nodes,ie,{...ie,value:String(ue[2])})}function B(_){if(!_||"word"!==_.type)return!1;if(!H(_))return!1;const X=ne.default.unit(_.value);return!!X&&("%"===X.unit||""===X.unit)}function E(_){return _&&"function"===_.type&&"calc"===_.value}function j(_){return _&&"function"===_.type&&"var"===_.value}function k(_){return _&&"div"===_.type&&"/"===_.value}function C(_){if(!B(_[0]))return null;if(!B(_[1]))return null;if(!function(_){if(!_||"word"!==_.type)return!1;if(!H(_))return!1;const X=ne.default.unit(_.value);return!(!X||!X.number||"deg"!==X.unit&&"grad"!==X.unit&&"rad"!==X.unit&&"turn"!==X.unit&&""!==X.unit)}(_[2]))return null;const X={l:ne.default.unit(_[0].value),lNode:_[0],c:ne.default.unit(_[1].value),cNode:_[1],h:ne.default.unit(_[2].value),hNode:_[2]};return function(_){switch(_.unit){case"deg":return void(_.unit="");case"rad":return _.unit="",void(_.number=(180*parseFloat(_.number)/Math.PI).toString());case"grad":return _.unit="",void(_.number=(.9*parseFloat(_.number)).toString());case"turn":_.unit="",_.number=(360*parseFloat(_.number)).toString()}}(X.h),""!==X.h.unit?null:(k(_[3])&&(X.slash=_[3]),(B(_[4])||E(_[4])||j(_[4]))&&(X.alpha=_[4]),!(_.length>3)||X.slash&&X.alpha?("%"===X.l.unit&&(X.l.unit=""),"%"===X.c.unit&&(X.c.unit="",X.c.number=(parseFloat(X.c.number)/100*150).toFixed(10)),X):null)}function $(_){if(!B(_[0]))return null;if(!B(_[1]))return null;if(!B(_[2]))return null;const X={l:ne.default.unit(_[0].value),lNode:_[0],a:ne.default.unit(_[1].value),aNode:_[1],b:ne.default.unit(_[2].value),bNode:_[2]};return k(_[3])&&(X.slash=_[3]),(B(_[4])||E(_[4])||j(_[4]))&&(X.alpha=_[4]),!(_.length>3)||X.slash&&X.alpha?("%"===X.l.unit&&(X.l.unit=""),"%"===X.a.unit&&(X.a.unit="",X.a.number=(parseFloat(X.a.number)/100*125).toFixed(10)),"%"===X.b.unit&&(X.b.unit="",X.b.number=(parseFloat(X.b.number)/100*125).toFixed(10)),X):null}function D(_){return void 0!==_.a}function G(_){return D(_)?[_.lNode,_.aNode,_.bNode]:[_.lNode,_.cNode,_.hNode]}function L(_){return D(_)?[_.l,_.a,_.b]:[_.l,_.c,_.h]}function z(_,X,ee){const te=_.indexOf(X);_[te]=ee}function H(_){if(!_||!_.value)return!1;try{return!1!==ne.default.unit(_.value)}catch(_){return!1}}function J(_,X,ee,te){let re;try{re=ne.default(_)}catch(te){X.warn(ee,`Failed to parse value '${_}' as a lab or lch function. Leaving the original value intact.`)}if(void 0===re)return;re.walk((_=>{_.type&&"function"===_.type&&("lab"!==_.value&&"lch"!==_.value||q(_))}));const se=String(re);if(se===_)return;const ie=ne.default(_);ie.walk((_=>{_.type&&"function"===_.type&&("lab"!==_.value&&"lch"!==_.value||function(_,X,ee,te){const re=ne.default.stringify(_),se=_.value,ie=_.nodes.slice().filter((_=>"comment"!==_.type&&"space"!==_.type));let oe=null;if("lab"===se?oe=$(ie):"lch"===se&&(oe=C(ie)),!oe)return;if(ie.length>3&&(!oe.slash||!oe.alpha))return;_.value="color";const[ae,le,ue]=G(oe),[ce,pe,fe]=L(oe),de="lab"===se?N:O,he=[ce.number,pe.number,fe.number].map((_=>parseFloat(_))),[me,ge]=de(he);!ge&&te&&X.warn(ee,`"${re}" is out of gamut for "display-p3". Given "preserve: true" is set, this will lead to unexpected results in some browsers.`),_.nodes.splice(0,0,{sourceIndex:0,sourceEndIndex:10,value:"display-p3",type:"word"}),_.nodes.splice(1,0,{sourceIndex:0,sourceEndIndex:1,value:" ",type:"space"}),z(_.nodes,ae,{...ae,value:me[0].toFixed(5)}),z(_.nodes,le,{...le,value:me[1].toFixed(5)}),z(_.nodes,ue,{...ue,value:me[2].toFixed(5)})}(_,X,ee,te))}));return{rgb:se,displayP3:String(ie)}}const K=_=>({postcssPlugin:"postcss-lab-function",Declaration:(X,{result:ee})=>{if(function(_){const X=_.parent;if(!X)return!1;const ee=X.index(_);for(let te=0;te<ee;te++){const ee=X.nodes[te];if("decl"===ee.type&&ee.prop===_.prop)return!0}return!1}(X))return;if(function(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("supports"===X.name){if(-1!==X.params.indexOf("lab("))return!0;if(-1!==X.params.indexOf("lch("))return!0}X=X.parent}else X=X.parent;return!1}(X))return;const te=X.value;if(!/(^|[^\w-])(lab|lch)\(/i.test(te))return;const re=J(te,X,ee,_.preserve);void 0!==re&&(_.preserve?(X.cloneBefore({value:re.rgb}),_.subFeatures.displayP3&&X.cloneBefore({value:re.displayP3})):(X.cloneBefore({value:re.rgb}),_.subFeatures.displayP3&&X.cloneBefore({value:re.displayP3}),X.remove()))}});K.postcss=!0;const Q=_=>{const X=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},_);return X.subFeatures=Object.assign({displayP3:!0},X.subFeatures),X.enableProgressiveCustomProperties&&(X.preserve||X.subFeatures.displayP3)?{postcssPlugin:"postcss-lab-function",plugins:[se.default(),K(X)]}:K(X)};Q.postcss=!0,_.exports=Q},2520:(_,X,ee)=>{"use strict";function r(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=r(ee(977)),t=(_,X)=>{const ee="rule"===Object(_.parent).type?_.parent.cloneBefore({raws:{}}).removeAll():te.default.rule({selector:"&"});return ee.selectors=ee.selectors.map((_=>`${_}:dir(${X})`)),ee};const re=/^border-(block|block-start|block-end|inline|inline-start|inline-end)(-(width|style|color))?$/i;var l=(_,X,ee,te)=>{_.cloneBefore({prop:`border-top${_.prop.replace(re,"$2")}`,value:X[0]}),_.cloneBefore({prop:`border-bottom${_.prop.replace(re,"$2")}`,value:X[1]||X[0]}),b(_,te)},n=(_,X,ee,te)=>{_.cloneBefore({prop:`border-top${_.prop.replace(re,"$2")}`}),b(_,te)},i=(_,X,ee,te)=>{_.cloneBefore({prop:`border-bottom${_.prop.replace(re,"$2")}`}),b(_,te)},d=(_,X,ee,te)=>{const i=()=>[_.cloneBefore({prop:`border-left${_.prop.replace(re,"$2")}`,value:X[0]}),_.cloneBefore({prop:`border-right${_.prop.replace(re,"$2")}`,value:X[1]||X[0]})],d=()=>[_.clone({prop:`border-right${_.prop.replace(re,"$2")}`,value:X[0]}),_.clone({prop:`border-left${_.prop.replace(re,"$2")}`,value:X[1]||X[0]})];return 1===X.length||2===X.length&&X[0]===X[1]||"ltr"===ee?(i(),void b(_,te)):"rtl"===ee?(d(),void b(_,te)):(t(_,"ltr").append(i()),t(_,"rtl").append(d()),void b(_,te))},p=(_,X,ee,te)=>{const i=()=>_.cloneBefore({prop:`border-left${_.prop.replace(re,"$2")}`}),d=()=>_.cloneBefore({prop:`border-right${_.prop.replace(re,"$2")}`});return"ltr"===ee?(i(),void b(_,te)):"rtl"===ee?(d(),void b(_,te)):(t(_,"ltr").append(i()),t(_,"rtl").append(d()),void b(_,te))},a=(_,X,ee,te)=>{const i=()=>_.cloneBefore({prop:`border-right${_.prop.replace(re,"$2")}`}),d=()=>_.cloneBefore({prop:`border-left${_.prop.replace(re,"$2")}`});return"ltr"===ee?(i(),void b(_,te)):"rtl"===ee?(d(),void b(_,te)):(t(_,"ltr").append(i()),t(_,"rtl").append(d()),void b(_,te))};function b(_,X){X||_.remove()}const se=/^(border-)(end-end|end-start|start-end|start-start)(-radius)$/i,ne={"end-end":"bottom-right","end-start":"bottom-left","start-end":"top-right","start-start":"top-left"},ie={"end-end":"bottom-left","end-start":"bottom-right","start-end":"top-left","start-start":"top-right"};var f=(_,X,ee,te)=>"ltr"===ee?(u(_),void v(_,te)):"rtl"===ee?(h(_),void v(_,te)):(t(_,"ltr").append(u(_)),t(_,"rtl").append(h(_)),void v(_,te));function u(_){return _.cloneBefore({prop:_.prop.replace(se,((_,X,ee,te)=>`${X}${ne[ee]}${te}`))})}function h(_){return _.cloneBefore({prop:_.prop.replace(se,((_,X,ee,te)=>`${X}${ie[ee]}${te}`))})}function v(_,X){X||_.remove()}var m=_=>{const X=_.slice();return 4===X.length&&X[3]===X[1]&&X.pop(),3===X.length&&X[2]===X[0]&&X.pop(),2===X.length&&X[1]===X[0]&&X.pop(),X},k=(_,X,ee,te)=>{if("logical"!==X[0])return null;const[,re,se,ne,ie]=X,oe=m([re,ie||se||re,ne||re,se||re]),b=()=>_.cloneBefore({value:oe.join(" ")});if(oe.length<4||"ltr"===ee)return b(),void $(_,te);const ae=m([re,se||re,ne||re,ie||se||re]),s=()=>_.cloneBefore({value:ae.join(" ")});if("rtl"===ee)return s(),void $(_,te);t(_,"ltr").append(b()),t(_,"rtl").append(s()),$(_,te)};function $(_,X){X||_.remove()}var B=(_,X,ee,te)=>/^inline-start$/i.test(_.value)?"ltr"===ee?(y(_),void j(_,te)):"rtl"===ee?(w(_),void j(_,te)):(t(_,"ltr").append(y(_)),t(_,"rtl").append(w(_)),void j(_,te)):/^inline-end$/i.test(_.value)?"ltr"===ee?(w(_),void j(_,te)):"rtl"===ee?(y(_),void j(_,te)):(t(_,"ltr").append(w(_)),t(_,"rtl").append(y(_)),void j(_,te)):void 0;function y(_){return _.cloneBefore({value:"left"})}function w(_){return _.cloneBefore({value:"right"})}function j(_,X){X||_.remove()}var z=(_,X,ee,te)=>{if("logical"!==X[0])return _.cloneBefore({prop:"top",value:X[0]}),_.cloneBefore({prop:"right",value:X[1]||X[0]}),_.cloneBefore({prop:"bottom",value:X[2]||X[0]}),_.cloneBefore({prop:"left",value:X[3]||X[1]||X[0]}),void O(_,te);return!X[4]||X[4]===X[2]||"ltr"===ee?(x(_,X),void O(_,te)):"rtl"===ee?(E(_,X),void O(_,te)):(t(_,"ltr").append(x(_,X)),t(_,"rtl").append(E(_,X)),void O(_,te))};function x(_,X){return[_.cloneBefore({prop:"top",value:X[1]}),_.cloneBefore({prop:"left",value:X[2]||X[1]}),_.cloneBefore({prop:"bottom",value:X[3]||X[1]}),_.cloneBefore({prop:"right",value:X[4]||X[2]||X[1]})]}function E(_,X){return[_.cloneBefore({prop:"top",value:X[1]}),_.cloneBefore({prop:"right",value:X[2]||X[1]}),_.cloneBefore({prop:"bottom",value:X[3]||X[1]}),_.cloneBefore({prop:"left",value:X[4]||X[2]||X[1]})]}function O(_,X){X||_.remove()}var q=(_,X,ee,te)=>/^block$/i.test(_.value)?(_.cloneBefore({value:"vertical"}),void A(_,te)):/^inline$/i.test(_.value)?(_.cloneBefore({value:"horizontal"}),void A(_,te)):void 0;function A(_,X){X||_.remove()}var oe=/^(inset|margin|padding)(?:-(block|block-start|block-end|inline|inline-start|inline-end|start|end))$/i,ae=/^inset-/i,C=(_,X,ee)=>_.cloneBefore({prop:`${_.prop.replace(oe,"$1")}${X}`.replace(ae,""),value:ee}),F=(_,X,ee,te)=>{C(_,"-top",X[0]),C(_,"-bottom",X[1]||X[0]),L(_,te)},G=(_,X,ee,te)=>{_.cloneBefore({prop:_.prop.replace(oe,"$1-top").replace(ae,"")}),L(_,te)},H=(_,X,ee,te)=>{_.cloneBefore({prop:_.prop.replace(oe,"$1-bottom").replace(ae,"")}),L(_,te)},I=(_,X,ee,te)=>{const n=()=>[C(_,"-left",X[0]),C(_,"-right",X[1]||X[0])],i=()=>[C(_,"-right",X[0]),C(_,"-left",X[1]||X[0])];return 1===X.length||2===X.length&&X[0]===X[1]||"ltr"===ee?(n(),void L(_,te)):"rtl"===ee?(i(),void L(_,te)):(t(_,"ltr").append(n()),t(_,"rtl").append(i()),void L(_,te))},J=(_,X,ee,te)=>{const n=()=>C(_,"-left",_.value),i=()=>C(_,"-right",_.value);return"ltr"===ee?(n(),void L(_,te)):"rtl"===ee?(i(),void L(_,te)):(t(_,"ltr").append(n()),t(_,"rtl").append(i()),void L(_,te))},K=(_,X,ee,te)=>{const n=()=>C(_,"-right",_.value),i=()=>C(_,"-left",_.value);return"ltr"===ee?(n(),void L(_,te)):"rtl"===ee?(i(),void L(_,te)):(t(_,"ltr").append(n()),t(_,"rtl").append(i()),void L(_,te))};function L(_,X){X||_.remove()}var le=/^(min-|max-)?(block|inline)-(size)$/i,N=(_,X,ee,te)=>{_.cloneBefore({prop:_.prop.replace(le,((_,X,ee)=>`${X||""}${"block"===ee?"height":"width"}`))}),te||_.remove()},Q=(_,X,ee,te)=>/^start$/i.test(_.value)?"ltr"===ee?(R(_),void T(_,te)):"rtl"===ee?(S(_),void T(_,te)):(t(_,"ltr").append(R(_)),t(_,"rtl").append(S(_)),void T(_,te)):/^end$/i.test(_.value)?"ltr"===ee?(S(_),void T(_,te)):"rtl"===ee?(R(_),void T(_,te)):(t(_,"ltr").append(S(_)),t(_,"rtl").append(R(_)),void T(_,te)):void 0;function R(_){return _.cloneBefore({value:"left"})}function S(_){return _.cloneBefore({value:"right"})}function T(_,X){X||_.remove()}function U(_,X){return V(_,/^\s$/,X)}function V(_,X,ee){const te=[];let re="",se=!1,ne=0,ie=-1;for(;++ie<_.length;){const oe=_[ie];"("===oe?ne+=1:")"===oe?ne>0&&(ne-=1):0===ne&&X.test(oe)&&(se=!0),se?(ee&&!re.trim()||te.push(ee?re.trim():re),ee||te.push(oe),re="",se=!1):re+=oe}return""!==re&&te.push(ee?re.trim():re),te}var W=(_,X,ee,te)=>{const re=[],se=[];var ne,ie;return(ne=_.value,V(ne,/^,$/,ie)).forEach((_=>{let X=!1;U(_).forEach(((_,ee,te)=>{_ in ue&&(X=!0,ue[_].ltr.forEach((_=>{const X=te.slice();X.splice(ee,1,_),re.length&&!/^,$/.test(re[re.length-1])&&re.push(","),re.push(X.join(""))})),ue[_].rtl.forEach((_=>{const X=te.slice();X.splice(ee,1,_),se.length&&!/^,$/.test(se[se.length-1])&&se.push(","),se.push(X.join(""))})))})),X||(re.push(_),se.push(_))})),re.length&&"ltr"===ee?(te&&_.cloneBefore({}),void(_.value=re.join(""))):se.length&&"rtl"===ee?(te&&_.cloneBefore({}),void(_.value=se.join(""))):re.join("")!==se.join("")?(t(_,"ltr").append(_.cloneBefore({value:re.join("")})),t(_,"rtl").append(_.cloneBefore({value:se.join("")})),void function(_,X){X||_.remove()}(_,te)):void 0};const ue={"block-size":{ltr:["height"],rtl:["height"]},"inline-size":{ltr:["width"],rtl:["width"]},"margin-block-end":{ltr:["margin-bottom"],rtl:["margin-bottom"]},"margin-block-start":{ltr:["margin-top"],rtl:["margin-top"]},"margin-block":{ltr:["margin-top","margin-bottom"],rtl:["margin-top","margin-bottom"]},"margin-inline-end":{ltr:["margin-right"],rtl:["margin-left"]},"margin-inline-start":{ltr:["margin-left"],rtl:["margin-right"]},"margin-inline":{ltr:["margin-left","margin-right"],rtl:["margin-left","margin-right"]},"inset-block-end":{ltr:["bottom"],rtl:["bottom"]},"inset-block-start":{ltr:["top"],rtl:["top"]},"inset-block":{ltr:["top","bottom"],rtl:["top","bottom"]},"inset-inline-end":{ltr:["right"],rtl:["left"]},"inset-inline-start":{ltr:["left"],rtl:["right"]},"inset-inline":{ltr:["left","right"],rtl:["left","right"]},inset:{ltr:["top","right","bottom","left"],rtl:["top","right","bottom","left"]},"padding-block-end":{ltr:["padding-bottom"],rtl:["padding-bottom"]},"padding-block-start":{ltr:["padding-top"],rtl:["padding-top"]},"padding-block":{ltr:["padding-top","padding-bottom"],rtl:["padding-top","padding-bottom"]},"padding-inline-end":{ltr:["padding-right"],rtl:["padding-left"]},"padding-inline-start":{ltr:["padding-left"],rtl:["padding-right"]},"padding-inline":{ltr:["padding-left","padding-right"],rtl:["padding-left","padding-right"]},"border-block-color":{ltr:["border-top-color","border-bottom-color"],rtl:["border-top-color","border-bottom-color"]},"border-block-end-color":{ltr:["border-bottom-color"],rtl:["border-bottom-color"]},"border-block-end-style":{ltr:["border-bottom-style"],rtl:["border-bottom-style"]},"border-block-end-width":{ltr:["border-bottom-width"],rtl:["border-bottom-width"]},"border-block-end":{ltr:["border-bottom"],rtl:["border-bottom"]},"border-block-start-color":{ltr:["border-top-color"],rtl:["border-top-color"]},"border-block-start-style":{ltr:["border-top-style"],rtl:["border-top-style"]},"border-block-start-width":{ltr:["border-top-width"],rtl:["border-top-width"]},"border-block-start":{ltr:["border-top"],rtl:["border-top"]},"border-block-style":{ltr:["border-top-style","border-bottom-style"],rtl:["border-top-style","border-bottom-style"]},"border-block-width":{ltr:["border-top-width","border-bottom-width"],rtl:["border-top-width","border-bottom-width"]},"border-block":{ltr:["border-top","border-bottom"],rtl:["border-top","border-bottom"]},"border-inline-color":{ltr:["border-left-color","border-right-color"],rtl:["border-left-color","border-right-color"]},"border-inline-end-color":{ltr:["border-right-color"],rtl:["border-left-color"]},"border-inline-end-style":{ltr:["border-right-style"],rtl:["border-left-style"]},"border-inline-end-width":{ltr:["border-right-width"],rtl:["border-left-width"]},"border-inline-end":{ltr:["border-right"],rtl:["border-left"]},"border-inline-start-color":{ltr:["border-left-color"],rtl:["border-right-color"]},"border-inline-start-style":{ltr:["border-left-style"],rtl:["border-right-style"]},"border-inline-start-width":{ltr:["border-left-width"],rtl:["border-right-width"]},"border-inline-start":{ltr:["border-left"],rtl:["border-right"]},"border-inline-style":{ltr:["border-left-style","border-right-style"],rtl:["border-left-style","border-right-style"]},"border-inline-width":{ltr:["border-left-width","border-right-width"],rtl:["border-left-width","border-right-width"]},"border-inline":{ltr:["border-left","border-right"],rtl:["border-left","border-right"]},"border-end-end-radius":{ltr:["border-bottom-right-radius"],rtl:["border-bottom-left-radius"]},"border-end-start-radius":{ltr:["border-bottom-left-radius"],rtl:["border-bottom-right-radius"]},"border-start-end-radius":{ltr:["border-top-right-radius"],rtl:["border-top-left-radius"]},"border-start-start-radius":{ltr:["border-top-left-radius"],rtl:["border-top-right-radius"]}};function Y(_){let X=_.parent;for(;X;)if("atrule"===X.type){if("keyframes"===X.name)return!0;X=X.parent}else X=X.parent;return!1}function Z(_){_=Object(_);const X=Boolean(_.preserve),ee=!X&&"string"==typeof _.dir&&(/^rtl$/i.test(_.dir)?"rtl":"ltr"),o=_=>te=>{if(Y(te))return;const re=te.parent,se=U(te.value,!0);_(te,se,ee,X),re.nodes.length||re.remove()},b=_=>te=>{if(Y(te))return;const re=te.parent,se=[te.value];_(te,se,ee,X),re.nodes.length||re.remove()};return{postcssPlugin:"postcss-logical-properties",Declaration:{clear:o(B),float:o(B),resize:o(q),"text-align":o(Q),"block-size":o(N),"max-block-size":o(N),"min-block-size":o(N),"inline-size":o(N),"max-inline-size":o(N),"min-inline-size":o(N),margin:o(k),"margin-inline":o(I),"margin-inline-end":o(K),"margin-inline-start":o(J),"margin-block":o(F),"margin-block-end":o(H),"margin-block-start":o(G),inset:o(z),"inset-inline":o(I),"inset-inline-end":o(K),"inset-inline-start":o(J),"inset-block":o(F),"inset-block-end":o(H),"inset-block-start":o(G),padding:o(k),"padding-inline":o(I),"padding-inline-end":o(K),"padding-inline-start":o(J),"padding-block":o(F),"padding-block-end":o(H),"padding-block-start":o(G),"border-block":b(l),"border-block-color":o(l),"border-block-style":o(l),"border-block-width":o(l),"border-block-end":b(i),"border-block-end-color":o(i),"border-block-end-style":o(i),"border-block-end-width":o(i),"border-block-start":b(n),"border-block-start-color":o(n),"border-block-start-style":o(n),"border-block-start-width":o(n),"border-inline":b(d),"border-inline-color":o(d),"border-inline-style":o(d),"border-inline-width":o(d),"border-inline-end":b(a),"border-inline-end-color":o(a),"border-inline-end-style":o(a),"border-inline-end-width":o(a),"border-inline-start":b(p),"border-inline-start-color":o(p),"border-inline-start-style":o(p),"border-inline-start-width":o(p),"border-end-end-radius":o(f),"border-end-start-radius":o(f),"border-start-end-radius":o(f),"border-start-start-radius":o(f),"border-color":o(k),"border-style":o(k),"border-width":o(k),transition:o(W),"transition-property":o(W)}}}Z.postcss=!0,_.exports=Z},94:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));function n(_){if(!_.nodes.length)return void _.remove();const X=_.nodes.filter((_=>"comment"===_.type));X.length===_.nodes.length&&_.replaceWith(...X)}function o(_){const X=_.parent,ee=X.index(_);if(ee){n(X.cloneBefore().removeAll().append(X.nodes.slice(0,ee)))}return X.before(_),X}function r(_,X){if(X<2)throw new Error("n must be greater than 1");if(_.length<2)throw new Error("s must be greater than 1");if(Math.pow(_.length,X)>1e4)throw new Error("Too many combinations when trying to resolve a nested selector with lists, reduce the complexity of your selectors");const ee=[];for(let _=0;_<X;_++)ee[_]=0;const te=[];for(;;){const re=[];for(let se=X-1;se>=0;se--){let X=ee[se];if(X>=_.length){if(X=0,ee[se]=0,0===se)return te;ee[se-1]+=1}re[se]=_[X]}te.push(re),ee[ee.length-1]++}}const re=te.default.pseudo({value:":is"});function a(_){const X=_.nodes.filter((_=>"tag"===_.type));X.length>1&&X.slice(1).forEach((_=>{const X=re.clone();_.replaceWith(X),X.append(_)}))}function l(_,X){let ee=[],re=!1;const se=[..._.nodes];for(let _=0;_<se.length+1;_++){const ne=se[_];if(ne&&"combinator"!==ne.type)"nesting"===ne.type&&(re=!0),ee.push(ne);else{if(re){ee=[];continue}if(ee.length>1){const _=te.default.selector();ee[0].replaceWith(_),ee.slice(1).forEach((_=>{_.remove()})),ee.forEach((X=>{_.append(X)})),c(_),X&&a(_),_.replaceWith(..._.nodes)}ee=[]}}}function c(_){_.nodes.sort(((_,X)=>i(_.value,_.type)-i(X.value,X.type)))}function i(_,X){return"pseudo"===X&&_&&0===_.indexOf("::")?se.pseudoElement:se[X]}const se={universal:0,tag:1,id:2,class:3,attribute:4,selector:5,pseudo:6,pseudoElement:7,string:8,root:9,comment:10,nesting:9999};function u(_){const X=_.map((_=>te.default().astSync(_))).map((_=>d(_))),ee=X[0];for(let _=1;_<X.length;_++)if(ee.a!==X[_].a||ee.b!==X[_].b||ee.c!==X[_].c)return!1;return!0}function d(_){let X=0,ee=0,re=0;if("universal"==_.type)return{a:0,b:0,c:0};if("id"===_.type)X+=1;else if("tag"===_.type)re+=1;else if("class"===_.type)ee+=1;else if("attribute"===_.type)ee+=1;else if("pseudo"===_.type&&0===_.value.indexOf("::"))re+=1;else if("pseudo"===_.type)switch(_.value){case":after":case":before":re+=1;break;case":is":case":has":case":not":if(_.nodes&&_.nodes.length>0){let te={a:0,b:0,c:0};_.nodes.forEach((_=>{const X=d(_);X.a>te.a?te=X:X.a<te.a||(X.b>te.b?te=X:X.b<te.b||X.c>te.c&&(te=X))})),X+=te.a,ee+=te.b,re+=te.c}break;case"where":break;case":nth-child":case":nth-last-child":{const se=_.nodes.findIndex((_=>{_.value}));if(se>-1){const ne=d(te.default.selector({nodes:_.nodes.slice(se+1),value:""}));X+=ne.a,ee+=ne.b,re+=ne.c}else X+=X,ee+=ee,re+=re}break;default:ee+=1}else _.nodes&&_.nodes.length>0&&_.nodes.forEach((_=>{const te=d(_);X+=te.a,ee+=te.b,re+=te.c}));return{a:X,b:ee,c:re}}function f(_,X,ee){let re=[];re=u(_)||ee.noIsPseudoSelector?_.map((_=>te.default().astSync(_))):[te.default().astSync(`:is(${_.join(",")})`)];let se=[];for(let _=0;_<X.length;_++){const ne=X[_];let ie=1,oe=[],ae=0;if(te.default().astSync(ne).walkNesting((()=>{ae++})),ae>1&&re.length>1)oe=r(re,ae),ie=oe.length;else{ie=re.length;for(let _=0;_<re.length;_++){oe.push([]);for(let X=0;X<ae;X++)oe[_].push(re[_])}}for(let _=0;_<ie;_++){let X=0;const re=te.default().astSync(ne);re.walk((re=>{if("nesting"!==re.type)return;let se=oe[_][X];X++,"root"===se.type&&1===se.nodes.length&&(se=se.nodes[0]);const ne=te.default().astSync(`:is(${se.toString()})`),ie=h(se.nodes[0]),ae=y(se.nodes[0]),le=h(re),ue=y(re);if(ie&&le)return void re.replaceWith(se.clone());if((ie||ae)&&(le||ue)){const _=re.parent;return ie&&"selector"===se.type?re.replaceWith(se.clone().nodes[0]):re.replaceWith(...se.clone().nodes),void(_&&_.nodes.length>1&&(c(_),ee.noIsPseudoSelector||a(_)))}if(ie){const _=re.parent;return re.replaceWith(se.clone().nodes[0]),void(_&&l(_,!ee.noIsPseudoSelector))}if(ae){const _=re.parent;return re.replaceWith(...se.clone().nodes),void(_&&l(_,!ee.noIsPseudoSelector))}if(m(re)){const _=re.parent;return re.replaceWith(...se.clone().nodes),void(_&&l(_,!ee.noIsPseudoSelector))}if(g(re)){const _=re.parent;return re.replaceWith(...se.clone().nodes),void(_&&l(_,!ee.noIsPseudoSelector))}const ce=re.parent;ee.noIsPseudoSelector?re.replaceWith(...se.clone().nodes):re.replaceWith(...ne.clone().nodes),ce&&l(ce,!ee.noIsPseudoSelector)})),se.push(re.toString())}}return se}function h(_){return"combinator"!==_.type&&!(_.parent&&_.parent.nodes.length>1)}function y(_,X=null){if(h(_))return!1;if(!_.parent)return!1;if(!!_.parent.nodes.find((_=>"combinator"===_.type||"comment"===_.type)))return!1;return!(!!_.parent.nodes.find((_=>"nesting"===_.type))&&X&&!y(X))}function m(_){if(!_.parent)return!1;if(0!==_.parent.nodes.indexOf(_))return!1;for(let X=1;X<_.parent.nodes.length;X++)if("combinator"===_.parent.nodes[X].type&&" "!==_.parent.nodes[X].value&&">"!==_.parent.nodes[X].value)return!1;return!0}function g(_){if(h(_))return!0;if(!_.parent)return!1;for(let X=0;X<_.parent.nodes.length;X++)if("nesting"!==!_.parent.nodes[X].type&&(_.parent.nodes[X].prev()||_.parent.nodes[X].next())){if(_.parent.nodes[X].prev()&&"combinator"!==_.parent.nodes[X].prev().type)return!1;if(_.parent.nodes[X].next()&&"combinator"!==_.parent.nodes[X].next().type)return!1}return!0}const b=_=>{let X=[],ee="",te=!1,re=0,se=!1,ne=!1;for(let ie of _)ne?ne=!1:"\\"===ie?ne=!0:se?ie===se&&(se=!1):'"'===ie||"'"===ie?se=ie:"("===ie?re+=1:")"===ie?re>0&&(re-=1):0===re&&","===ie&&(te=!0),te?(""!==ee&&X.push(ee.trim()),ee="",te=!1):ee+=ie;return X.push(ee.trim()),X};var ne=["container","document","media","supports"];function S(_){const X=o(_);var ee,te;_.params=(ee=X.params,te=_.params,b(ee).map((_=>b(te).map((X=>`${_} and ${X}`)).join(", "))).join(", ")),n(X)}function W(_,X){_.each((_=>{(_=>"rule"===_.type&&"rule"===Object(_.parent).type&&_.selectors.every((_=>0===_.trim().indexOf("&")&&-1===_.indexOf("|"))))(_)?function(_,X){const ee=o(_);_.selectors=f(ee.selectors,_.selectors,X),("rule"===_.type&&"rule"===ee.type&&_.selector===ee.selector||"atrule"===_.type&&"atrule"===ee.type&&_.params===ee.params)&&_.append(...ee.nodes),n(ee)}(_,X):(_=>"atrule"===_.type&&"nest"===_.name&&"rule"===Object(_.parent).type&&b(_.params).every((_=>_.split("&").length>=2&&-1===_.indexOf("|"))))(_)?function(_,X,ee){const te=o(_),re=te.clone().removeAll().append(_.nodes);_.replaceWith(re),re.selectors=f(te.selectors,b(_.params),ee),n(te),X(re,ee)}(_,W,X):(_=>"atrule"===_.type&&ne.includes(_.name)&&"rule"===Object(_.parent).type)(_)?function(_,X,ee){const te=o(_),re=te.clone().removeAll().append(_.nodes);_.append(re),n(te),X(re,ee)}(_,W,X):(_=>"atrule"===_.type&&ne.includes(_.name)&&"atrule"===Object(_.parent).type&&_.name===_.parent.name)(_)&&S(_),Object(_.nodes).length&&W(_,X)}))}function w(_){const X=Object(_).noIsPseudoSelector||!1;return{postcssPlugin:"postcss-nesting",Rule(_){W(_,{noIsPseudoSelector:X})}}}w.postcss=!0,_.exports=w},5698:_=>{"use strict";const X=/^overflow/i;const o=_=>{const ee=!("preserve"in Object(_))||Boolean(_.preserve);return{postcssPlugin:"postcss-overflow-shorthand",Declaration:(_,{list:te})=>{X.test(_)&&function(_,X,ee){const[te,re,...se]=_.space(X.value);re&&!se.length&&(X.cloneBefore({prop:`${X.prop}-x`,value:te}),X.cloneBefore({prop:`${X.prop}-y`,value:re}),ee||X.remove())}(te,_,ee)}}};o.postcss=!0,_.exports=o},6681:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(2045)),re={preserve:!0};const se=/^place-(content|items|self)/,l=_=>("preserve"in Object(_)&&(re.preserve=Boolean(_.preserve)),{postcssPlugin:"postcss-place",Declaration:(_,{result:X})=>{se.test(_)&&((_,{result:X})=>{const ee=_.prop.match(se)[1];let ne;try{ne=te.default(_.value)}catch(ee){_.warn(X,`Failed to parse value '${_.value}'. Leaving the original value intact.`)}if(void 0===ne)return;let ie=[];ie=ne.nodes.length?ne.nodes.filter((_=>"word"===_.type||"function"===_.type)).map((_=>te.default.stringify(_))):[te.default.stringify(ne)],_.cloneBefore({prop:`align-${ee}`,value:ie[0]}),_.cloneBefore({prop:`justify-${ee}`,value:ie[1]||ie[0]}),re.preserve||_.remove()})(_,{result:X})}});l.postcss=!0,_.exports=l},1097:(_,X,ee)=>{"use strict";var te=ee(6544),re=ee(2760),se=ee(5449),ne=ee(7147),ie=ee(1017),oe=ee(4907),ae=ee(6924),le=ee(3570),ue=ee(4836),ce=ee(2060),pe=ee(7106),fe=ee(5671),de=ee(8806),he=ee(8179),me=ee(50),ge=ee(1426),be=ee(3365),ve=ee(3073),ye=ee(8742),we=ee(6033),xe=ee(9060),ke=ee(3318),Se=ee(6008),_e=ee(8633),Pe=ee(6157),Oe=ee(2520),je=ee(9142),Te=ee(94),Ee=ee(5698),Fe=ee(971),$e=ee(6681),Me=ee(8277),Re=ee(6788),Ae=ee(3181),qe=ee(3991),ze=ee(2238),Ge=ee(434),Ue=ee(4658),He=ee(4719),Ze=ee(3345),Ke=ee(3942),Xe=ee(5378),et=ee(8078),tt=ee(1758);function L(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var rt=L(te),st=L(re),nt=L(se),it=L(ne),ot=L(ie),lt=L(oe),ut=L(ae),ct=L(le),pt=L(ue),ft=L(ce),dt=L(pe),ht=L(fe),mt=L(de),gt=L(he),bt=L(me),vt=L(ge),yt=L(be),wt=L(ve),xt=L(ye),kt=L(we),St=L(xe),_t=L(ke),Pt=L(Se),Ot=L(_e),Ct=L(Pe),jt=L(Oe),Tt=L(je),Et=L(Te),Ft=L(Ee),$t=L(Fe),Mt=L($e),Rt=L(Me),At=L(Re),Dt=L(Ae),qt=L(qe),It=L(ze),Nt=L(Ge),Lt=L(Ue),Bt=L(He),Vt=L(Ze),zt=L(Ke),Wt=L(Xe),Gt=L(et),Ut=L(tt);const Qt={"blank-pseudo-class":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-blank-pseudo/README-BROWSER.md","focus-visible-pseudo-class":"https://github.com/WICG/focus-visible","focus-within-pseudo-class":"https://github.com/jsxtools/focus-within/blob/master/README-BROWSER.md","has-pseudo-class":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-has-pseudo/README-BROWSER.md","prefers-color-scheme-query":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-prefers-color-scheme/README-BROWSER.md"},Yt=["blank-pseudo-class","focus-visible-pseudo-class","focus-within-pseudo-class","has-pseudo-class","prefers-color-scheme-query"];async function Ne(_,X,ee,te){const re=function(_){return`:root {\n${Object.keys(_).reduce(((X,ee)=>(X.push(`\t${ee}: ${_[ee]};`),X)),[]).join("\n")}\n}\n`}(ee),se=function(_){return`${Object.keys(_).reduce(((X,ee)=>(X.push(`@custom-media ${ee} ${_[ee]};`),X)),[]).join("\n")}\n`}(X),ne=function(_){return`${Object.keys(_).reduce(((X,ee)=>(X.push(`@custom-selector ${ee} ${_[ee]};`),X)),[]).join("\n")}\n`}(te),ie=`${se}\n${ne}\n${re}`;await Ve(_,ie)}function Be(_,X){return`\n\t${_}: {\n${Object.keys(X).reduce(((_,ee)=>(_.push(`\t\t'${De(ee)}': '${De(X[ee])}'`),_)),[]).join(",\n")}\n\t}`}function We(_,X){return`export const ${_} = {\n${Object.keys(X).reduce(((_,ee)=>(_.push(`\t'${De(ee)}': '${De(X[ee])}'`),_)),[]).join(",\n")}\n};\n`}function Ce(_,X){return Promise.all([].concat(X).map((async X=>{if(X instanceof Function)await X({customMedia:Ie(_.customMedia),customProperties:Ie(_.customProperties),customSelectors:Ie(_.customSelectors)});else{const ee=X===Object(X)?X:{to:String(X)},te=ee.toJSON||Ie;if("customMedia"in ee||"customProperties"in ee||"customSelectors"in ee)ee.customMedia=te(_.customMedia),ee.customProperties=te(_.customProperties),ee.customSelectors=te(_.customSelectors);else if("custom-media"in ee||"custom-properties"in ee||"custom-selectors"in ee)ee["custom-media"]=te(_.customMedia),ee["custom-properties"]=te(_.customProperties),ee["custom-selectors"]=te(_.customSelectors);else{const X=String(ee.to||""),re=(ee.type||ot.default.extname(ee.to).slice(1)).toLowerCase(),se=te(_.customMedia),ne=te(_.customProperties),ie=te(_.customSelectors);"css"===re&&await Ne(X,se,ne,ie),"js"===re&&await async function(_,X,ee,te){const re=`module.exports = {${Be("customMedia",X)},${Be("customProperties",ee)},${Be("customSelectors",te)}\n};\n`;await Ve(_,re)}(X,se,ne,ie),"json"===re&&await async function(_,X,ee,te){const re=`${JSON.stringify({"custom-media":X,"custom-properties":ee,"custom-selectors":te},null,"  ")}\n`;await Ve(_,re)}(X,se,ne,ie),"mjs"===re&&await async function(_,X,ee,te){const re=`${We("customMedia",X)}\n${We("customProperties",ee)}\n${We("customSelectors",te)}`;await Ve(_,re)}(X,se,ne,ie)}}})))}function Ie(_){return Object.keys(_).reduce(((X,ee)=>(X[ee]=String(_[ee]),X)),{})}function Ve(_,X){return new Promise(((ee,te)=>{it.default.writeFile(_,X,(_=>{_?te(_):ee()}))}))}function De(_){return _.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r")}function Le(_,X){if(!_)return!1;if("string"==typeof _)return!0;if(Array.isArray(_)){for(let ee=0;ee<_.length;ee++){if("string"==typeof _[ee])return!0;if(_[ee]&&X in Object(_[ee]))return!0}return!1}return X in Object(_)}function Je(_,X,ee){return Math.max(_,Math.min(X,ee))}const Jt=Symbol("insertBefore"),Ht=Symbol("insertAfter"),Zt=Symbol("insertOrder"),Kt=Symbol("plugin");function Qe(_,X,ee){if("insertBefore"!==ee&&"insertAfter"!==ee)return[];const te="insertBefore"===ee?Jt:Ht,re=[];for(const ee in X){if(!Object.hasOwnProperty.call(X,ee))continue;if(!_.find((_=>_.id===ee)))continue;let se=X[ee];Array.isArray(se)||(se=[se]);for(let _=0;_<se.length;_++)re.push({id:ee,[Kt]:se[_],[Zt]:_,[te]:!0})}return re}var Xt=["custom-media-queries","custom-properties","environment-variables","image-set-function","media-query-ranges","prefers-color-scheme-query","nesting-rules","custom-selectors","any-link-pseudo-class","case-insensitive-attributes","focus-visible-pseudo-class","focus-within-pseudo-class","not-pseudo-class","logical-properties-and-values","dir-pseudo-class","all-property","color-functional-notation","double-position-gradients","hexadecimal-alpha-notation","hwb-function","lab-function","rebeccapurple-color","blank-pseudo-class","break-properties","font-variant-property","is-pseudo-class","has-pseudo-class","gap-properties","overflow-property","overflow-wrap-property","place-properties","system-ui-font-family"];function Ye(){return{postcssPlugin:"postcss-system-ui-font",Declaration(_){er.test(_.prop)&&(_.value.includes(rr.join(", "))||(_.value=_.value.replace(sr,nr)))}}}Ye.postcss=!0;const er=/(?:^(?:-|\\002d){2})|(?:^font(?:-family)?$)/i,tr="[\\f\\n\\r\\x09\\x20]",rr=["system-ui","-apple-system","Segoe UI","Roboto","Ubuntu","Cantarell","Noto Sans","sans-serif"],sr=new RegExp(`(^|,|${tr}+)(?:system-ui${tr}*)(?:,${tr}*(?:${rr.join("|")})${tr}*)?(,|$)`,"i"),nr=`$1${rr.join(", ")}$2`,ir=new Map([["all-property",Ot.default],["any-link-pseudo-class",At.default],["blank-pseudo-class",ct.default],["break-properties",$t.default],["case-insensitive-attributes",ut.default],["clamp",Bt.default],["color-function",Wt.default],["color-functional-notation",pt.default],["custom-media-queries",ht.default],["custom-properties",mt.default],["custom-selectors",gt.default],["dir-pseudo-class",bt.default],["display-two-values",zt.default],["double-position-gradients",vt.default],["environment-variables",yt.default],["focus-visible-pseudo-class",wt.default],["focus-within-pseudo-class",xt.default],["font-format-keywords",Vt.default],["font-variant-property",kt.default],["gap-properties",St.default],["hwb-function",Nt.default],["has-pseudo-class",_t.default],["hexadecimal-alpha-notation",ft.default],["ic-unit",Ut.default],["image-set-function",Pt.default],["is-pseudo-class",It.default],["lab-function",Ct.default],["logical-properties-and-values",jt.default],["media-query-ranges",Tt.default],["nesting-rules",Et.default],["not-pseudo-class",qt.default],["oklab-function",Gt.default],["opacity-percentage",Lt.default],["overflow-property",Ft.default],["overflow-wrap-property",Dt.default],["place-properties",Mt.default],["prefers-color-scheme-query",Rt.default],["rebeccapurple-color",dt.default],["system-ui-font-family",Ye]]);function is(_,X,ee){return _.concat(Qe(_,X,"insertBefore"),Qe(_,ee,"insertAfter")).filter((_=>function(_){return!!_[Jt]||!!_[Ht]||!!ir.has(_.id)}(_))).sort(((_,X)=>function(_,X){return _.id===X.id?_[Jt]&&X[Jt]||_[Ht]&&X[Ht]?Je(-1,_[Zt]-X[Zt],1):_[Jt]||X[Ht]?-1:_[Ht]||X[Jt]?1:0:Je(-1,Xt.indexOf(_.id)-Xt.indexOf(X.id),1)}(_,X)))}const or=["ie","edge","firefox","chrome","safari","opera","ios_saf","android","op_mob","and_chr","and_ff","and_uc","samsung","and_qq","baidu","kaios"];function cs(_){if(!_)return[];if(!("browser_support"in _))return["> 0%"];const X=[];return or.forEach((ee=>{const te=_.browser_support[ee];"string"==typeof te&&/^[0-9|.]+$/.test(te)?X.push(`${ee} < ${_.browser_support[ee]}`):X.push(`${ee} >= 1`)})),X}function us(_,X,ee,te){const re=lt.default(_,{ignoreUnknownVersions:!0});switch(X.id){case"is-pseudo-class":return{onComplexSelector:"warning"};case"nesting-rules":if(function(_,X){const ee=cs(_);if(X.some((_=>lt.default(ee,{ignoreUnknownVersions:!0}).some((X=>X===_)))))return!0;return!1}(ee.find((_=>"is-pseudo-class"===_.id)),re))return te.log('Disabling :is on "nesting-rules" due to lack of browser support.'),{noIsPseudoSelector:!0};return{};case"any-link-pseudo-class":if(re.find((_=>_.startsWith("ie ")||_.startsWith("edge "))))return te.log('Adding area[href] fallbacks for ":any-link" support in Edge and IE.'),{subFeatures:{areaHrefNeedsFixing:!0}};return{};default:return{}}}function as(_,X,ee,te){const re=Object(X.features),se=!("enableClientSidePolyfills"in X)||X.enableClientSidePolyfills,ne=Object(X.insertBefore),ie=Object(X.insertAfter),oe=X.browsers,ae=Je(0,function(_){const X=parseInt(_,10);return Number.isNaN(X)?0:X}(X.minimumVendorImplementations),3);ae>0&&te.log(`Using features with ${ae} or more vendor implementations`);const le=function(_,X){let ee=2;if(void 0===_.stage)return X.log(`Using features from Stage ${ee} (default)`),ee;if(!1===_.stage)ee=5;else{let X=parseInt(_.stage,10);Number.isNaN(X)&&(X=0),ee=Je(0,X,5)}return 5===ee?X.log('Stage has been disabled, features will be handled via the "features" option.'):X.log(`Using features from Stage ${ee}`),ee}(X,te);2===le&&ee&&!1===ee.preserve&&(_=JSON.parse(JSON.stringify(_))).forEach((_=>{("blank-pseudo-class"===_.id||"prefers-color-scheme-query"===_.id)&&(_.stage=1)}));const ue=is(_,ne,ie).map((_=>function(_){const X=cs(_);if(_[Jt]||_[Ht]){let ee=_.id;return ee=_.insertBefore?`before-${ee}`:`after-${ee}`,{browsers:X,vendors_implementations:_.vendors_implementations,plugin:_[Kt],id:ee,stage:6}}return{browsers:X,vendors_implementations:_.vendors_implementations,plugin:ir.get(_.id),id:_.id,stage:_.stage}}(_))).filter((_=>0===ae||(!(!_[Jt]&&!_[Ht])||(ae<=_.vendors_implementations||(re[_.id]?(te.log(`  ${_.id} does not meet the required vendor implementations but has been enabled by options`),!0):(te.log(`  ${_.id} with ${_.vendors_implementations} vendor implementations has been disabled`),!1)))))).filter((_=>{const X=_.stage>=le,ee=se||!Yt.includes(_.id),ne=!1===re[_.id],ie=re[_.id]?re[_.id]:X&&ee;return ne?te.log(`  ${_.id} has been disabled by options`):X?ee||te.log(`  ${_.id} has been disabled by "enableClientSidePolyfills: false".`):ie?te.log(`  ${_.id} does not meet the required stage but has been enabled by options`):te.log(`  ${_.id} with stage ${_.stage} has been disabled`),ie})).map((X=>function(_,X,ee,te,re,se){let ne,ie;return ne=us(X,te,_,se),!0===ee[te.id]?re&&(ne=Object.assign({},ne,re)):ne=re?Object.assign({},ne,re,ee[te.id]):Object.assign({},ne,ee[te.id]),ne.enableProgressiveCustomProperties=!1,ie=te.plugin.postcss&&"function"==typeof te.plugin?te.plugin(ne):te.plugin&&te.plugin.default&&"function"==typeof te.plugin.default&&te.plugin.default.postcss?te.plugin.default(ne):te.plugin,{browsers:te.browsers,vendors_implementations:te.vendors_implementations,plugin:ie,pluginOptions:ne,id:te.id}}(_,oe,re,X,ee,te))),ce=lt.default(oe,{ignoreUnknownVersions:!0});return ue.filter((_=>{if(_.id in re)return re[_.id];if(function(_){if("importFrom"in Object(_.pluginOptions))switch(_.id){case"custom-media-queries":if(Le(_.pluginOptions.importFrom,"customMedia"))return!0;break;case"custom-properties":if(Le(_.pluginOptions.importFrom,"customProperties"))return!0;break;case"environment-variables":if(Le(_.pluginOptions.importFrom,"environmentVariables"))return!0;break;case"custom-selectors":if(Le(_.pluginOptions.importFrom,"customSelectors"))return!0}if("exportTo"in Object(_.pluginOptions))switch(_.id){case"custom-media-queries":if(Le(_.pluginOptions.exportTo,"customMedia"))return!0;break;case"custom-properties":if(Le(_.pluginOptions.exportTo,"customProperties"))return!0;break;case"environment-variables":if(Le(_.pluginOptions.exportTo,"environmentVariables"))return!0;break;case"custom-selectors":if(Le(_.pluginOptions.exportTo,"customSelectors"))return!0}return!1}(_))return!0;const X=lt.default(_.browsers,{ignoreUnknownVersions:!0}),ee=ce.some((_=>X.some((X=>X===_))));return ee||te.log(`${_.id} disabled due to browser support`),ee}))}class ls{constructor(){this.logs=[]}log(_){this.logs.push(_)}resetLogger(){this.logs.length=0}dumpLogs(_){_&&this.logs.forEach((X=>_.warn(X))),this.resetLogger()}}const ar={"css-blank-pseudo":"blank-pseudo-class","css-has-pseudo":"has-pseudo-class","css-prefers-color-scheme":"prefers-color-scheme-query","postcss-attribute-case-insensitive":"case-insensitive-attributes","postcss-clamp":"clamp","postcss-color-function":"color-function","postcss-color-functional-notation":"color-functional-notation","postcss-color-hex-alpha":"hexadecimal-alpha-notation","postcss-color-rebeccapurple":"rebeccapurple-color","postcss-custom-media":"custom-media-queries","postcss-custom-properties":"custom-properties","postcss-custom-selectors":"custom-selectors","postcss-dir-pseudo-class":"dir-pseudo-class","postcss-double-position-gradients":"double-position-gradients","postcss-env-function":"environment-variables","postcss-focus-visible":"focus-visible-pseudo-class","postcss-focus-within":"focus-within-pseudo-class","postcss-font-format-keywords":"font-format-keywords","postcss-font-variant":"font-variant-property","postcss-gap-properties":"gap-properties","postcss-hwb-function":"hwb-function","postcss-ic-unit":"ic-unit","postcss-image-set-function":"image-set-function","postcss-initial":"all-property","postcss-is-pseudo-class":"is-pseudo-class","postcss-lab-function":"lab-function","postcss-logical":"logical-properties-and-values","postcss-media-minmax":"media-query-ranges","postcss-nesting":"nesting-rules","postcss-normalize-display-values":"display-two-values","postcss-oklab-function":"oklab-function","postcss-opacity-percentage":"opacity-percentage","postcss-overflow-shorthand":"overflow-property","postcss-page-break":"break-properties","postcss-place":"place-properties","postcss-pseudo-class-any-link":"any-link-pseudo-class","postcss-replace-overflow-wrap":"overflow-wrap-property","postcss-selector-not":"not-pseudo-class","postcss-system-ui-font-family":"system-ui-font-family"},lr=(()=>{const _={};for(const[X,ee]of Object.entries(ar))_[ee]=X;return _})();function ds(_,X){let ee="unknown",te=1/0;for(let re=0;re<X.length;re++){const se=ms(_,X[re]);se<te&&(te=se,ee=X[re])}return{mostSimilar:ee,distance:te}}function ms(_,X){if(!_.length)return X.length;if(!X.length)return _.length;const ee=[];for(let te=0;te<=X.length;te++){ee[te]=[te];for(let re=1;re<=_.length;re++)ee[te][re]=0===te?re:Math.min(ee[te-1][re]+1,ee[te][re-1]+1,ee[te-1][re-1]+(_[re-1]===X[te-1]?0:1))}return ee[X.length][_.length]}const gs=_=>{const X=new ls,ee=Object(_),te=Object.keys(Object(ee.features)),re=ee.browsers,se=function(_){if("importFrom"in _||"exportTo"in _||"preserve"in _){const X={};return"importFrom"in _&&(X.importFrom=_.importFrom),"exportTo"in _&&(X.exportTo={customMedia:{},customProperties:{},customSelectors:{}}),"preserve"in _&&(X.preserve=_.preserve),X}return!1}(ee),ne=as(st.default,ee,se,X),ie=ne.map((_=>_.plugin));!1!==ee.autoprefixer&&ie.push(rt.default(Object.assign({overrideBrowserslist:re},ee.autoprefixer))),ie.push(nt.default()),function(_,X,ee){if(X.debug){ee.log("Enabling the following feature(s):");const X=[];_.forEach((_=>{_.id.startsWith("before")||_.id.startsWith("after")?ee.log(`  ${_.id} (injected via options)`):ee.log(`  ${_.id}`),void 0!==Qt[_.id]&&X.push(_.id)})),X.length&&(ee.log("These feature(s) need a browser library to work:"),X.forEach((_=>ee.log(` ${_}: ${Qt[_]}`))))}}(ne,ee,X);const u=()=>({postcssPlugin:"postcss-preset-env",OnceExit:function(re,{result:ne}){!function(_,X,ee){const te=Object.keys(lr),re=Object.keys(ar);_.forEach((_=>{if(te.includes(_))return;const se=ds(_,te),ne=ds(_,re);Math.min(se.distance,ne.distance)>10?X.warn(ee`Unknown feature: "${_}", see the list of features https://github.com/csstools/postcss-plugins/blob/main/plugin-packs/postcss-preset-env/FEATURES.md`):se.distance<ne.distance?X.warn(ee,`Unknown feature: "${_}", did you mean: "${se.mostSimilar}"`):X.warn(ee,`Unknown feature: "${_}", did you mean: "${ar[ne.mostSimilar]}"`)}))}(te,re,ne),ee.debug&&X.dumpLogs(ne),X.resetLogger(),ee.exportTo&&Ce(se.exportTo,_.exportTo)}});return u.postcss=!0,{postcssPlugin:"postcss-preset-env",plugins:[...ie,u()]}};gs.postcss=!0,_.exports=gs},6788:(_,X,ee)=>{"use strict";function e(_){return _&&"object"==typeof _&&"default"in _?_:{default:_}}var te=e(ee(5418));function n(_){if(!_||!_.nodes)return;let X=[];const ee=[..._.nodes];for(let _=0;_<ee.length+1;_++){const re=ee[_];if(re&&"combinator"!==re.type)X.push(re);else{if(X.length>1){const _=te.default.selector({value:""});X[0].replaceWith(_),X.slice(1).forEach((_=>{_.remove()})),X.forEach((X=>{_.append(X)})),s(_),_.replaceWith(..._.nodes)}X=[]}}}function s(_){_&&_.nodes&&_.nodes.sort(((_,X)=>{if("selector"===_.type&&"selector"===X.type&&_.nodes.length&&X.nodes.length){if(_.nodes[0].type===X.nodes[0].type)return 0;if(re[_.nodes[0].type]<re[X.nodes[0].type])return-1;if(re[_.nodes[0].type]>re[X.nodes[0].type])return 1}if("selector"===_.type&&_.nodes.length){if(_.nodes[0].type===X.type)return 0;if(re[_.nodes[0].type]<re[X.type])return-1;if(re[_.nodes[0].type]>re[X.type])return 1}if("selector"===X.type&&X.nodes.length){if(_.type===X.nodes[0].type)return 0;if(re[_.type]<re[X.nodes[0].type])return-1;if(re[_.type]>re[X.nodes[0].type])return 1}return _.type===X.type?0:re[_.type]<re[X.type]?-1:1}))}const re={universal:0,tag:1,id:2,class:3,attribute:4,pseudo:5,selector:7,string:8,root:9,comment:10},se=te.default().astSync(":link").nodes[0],ne=te.default().astSync(":visited").nodes[0],ie=te.default().astSync("area[href]").nodes[0];function u(_,X){let ee=[];return te.default((_=>{let te=0;if(_.walkPseudos((_=>{":any-link"!==_.value||_.nodes&&_.nodes.length||te++})),!te)return;let re=[];for(let _=0;_<te;_++)X?re.push([se.clone(),ne.clone(),ie.clone()]):re.push([se.clone(),ne.clone()]);(function(..._){const X=[],ee=_.length-1;function s(te,re){for(let se=0,ne=_[re].length;se<ne;se++){const ne=te.slice(0);ne.push(_[re][se]),re==ee?X.push(ne):s(ne,re+1)}}return s([],0),X})(...re).forEach((X=>{const te=_.clone();te.walkPseudos((_=>{":any-link"!==_.value||_.nodes&&_.nodes.length||_.replaceWith(...X.shift().nodes)})),te.walk((_=>{"nodes"in _&&(_.nodes.forEach((_=>{n(_)})),n(_))})),ee.push(te.toString())}))})).processSync(_),ee}const oe=/:any-link/;function p(_){const X={preserve:!0,..._},ee={areaHrefNeedsFixing:!1,...Object(X.subFeatures)};return{postcssPlugin:"postcss-pseudo-class-any-link",Rule(_,{result:te}){if(!oe.test(_.selector))return;const re=_.raws.selector&&_.raws.selector.raw||_.selector;":"!==re[re.length-1]&&function(_,X,ee,te){let re=[],se=[];try{for(let X=0;X<_.selectors.length;X++){const ee=_.selectors[X],ne=u(ee,te);ne.length?re.push(...ne):se.push(ee)}}catch(ee){return void _.warn(X,`Failed to parse selector : ${_.selector}`)}re.length&&(_.cloneBefore({selectors:re}),ee?se.length&&_.cloneBefore({selectors:se}):se.length?_.selectors=se:_.remove())}(_,te,X.preserve,ee.areaHrefNeedsFixing)}}}p.postcss=!0,_.exports=p},2760:_=>{"use strict";_.exports=JSON.parse('[{"id":"all-property","title":"`all` Property","description":"A property for defining the reset of all properties of an element","specification":"https://www.w3.org/TR/css-cascade-3/#all-shorthand","stage":4,"browser_support":{"edge":"79","firefox":"27","chrome":"37","safari":"9.1","opera":"24","ios_saf":"9.3","android":"4.4.3","op_mob":"64","and_chr":"37","and_ff":"27","and_uc":"12.12","samsung":"4","and_qq":"10.4","baidu":"7.12","kaios":"2.5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/all"},"example":"a {\\n  all: initial;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/maximkoretskiy/postcss-initial"}],"vendors_implementations":3},{"id":"any-link-pseudo-class","title":"`:any-link` Hyperlink Pseudo-Class","description":"A pseudo-class for matching anchor elements independent of whether they have been visited","specification":"https://www.w3.org/TR/selectors-4/#any-link-pseudo","stage":2,"browser_support":{"chrome":"1","and_chr":"18","edge":"79","firefox":"1","and_ff":"4","opera":"15","op_mob":"14","safari":"3","ios_saf":"1","samsung":"1.0","android":"65"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:any-link"},"example":"nav :any-link > span {\\n  background-color: yellow;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-pseudo-class-any-link"}],"vendors_implementations":3},{"id":"blank-pseudo-class","title":"`:blank` Empty-Value Pseudo-Class","description":"A pseudo-class for matching form elements when they are empty","specification":"https://www.w3.org/TR/selectors-4/#blank","stage":2,"browser_support":{},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:blank"},"example":"input:blank {\\n  background-color: yellow;\\n}","polyfills":[{"type":"JavaScript Library","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/css-blank-pseudo"},{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/css-blank-pseudo"}],"vendors_implementations":0},{"id":"break-properties","title":"Break Properties","description":"Properties for defining the break behavior between and within boxes","specification":"https://www.w3.org/TR/css-break-3/#breaking-controls","stage":3,"browser_support":{"ie":"10","edge":"12","safari":"10","opera":"11.1","ios_saf":"10","op_mini":"all","op_mob":"11.1","ie_mob":"10","and_uc":"12.12","samsung":"5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/break-after"},"example":"a {\\n  break-inside: avoid;\\n  break-before: avoid-column;\\n  break-after: always;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/shrpne/postcss-page-break"}],"vendors_implementations":1},{"id":"cascade-layers","title":"CSS Cascade Layers","description":"The `@layer` at-rule allows authors to explicitly layer their styles in the cascade, before specificity and order of appearance are considered.","specification":"https://www.w3.org/TR/css-cascade-5/#layering","stage":2,"browser_support":{"edge":"99","firefox":"97","chrome":"99","safari":"15.4","ios_saf":"15.4","android":"99","and_chr":"99"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/@layer"},"example":"/* Un-layered styles have the highest priority */\\na {\\n  color: mediumvioletred;\\n}\\n\\n@layer defaults {\\n  a { color: maroon; }\\n}","polyfills":[],"vendors_implementations":3},{"id":"case-insensitive-attributes","title":"Case-Insensitive Attributes","description":"An attribute selector matching attribute values case-insensitively","specification":"https://www.w3.org/TR/selectors-4/#attribute-case","stage":2,"browser_support":{"edge":"79","firefox":"47","chrome":"49","safari":"9","opera":"36","ios_saf":"9","android":"49","op_mob":"64","and_chr":"49","and_ff":"47","and_uc":"12.12","samsung":"5","and_qq":"10.4","kaios":"2.5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/Attribute_selectors"},"example":"[frame=hsides i] {\\n  border-style: solid none;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/Semigradsky/postcss-attribute-case-insensitive"}],"vendors_implementations":3},{"id":"clamp","title":"`clamp` Function","description":"The clamp() CSS function clamps a value between an upper and lower bound. It enables selecting a middle value within a range of values between a defined minimum and maximum.","specification":"https://www.w3.org/TR/css-values-4/#funcdef-clamp","stage":2,"browser_support":{"chrome":"79","and_chr":"79","edge":"79","firefox":"75","and_ff":"79","opera":"66","op_mob":"57","safari":"13.1","ios_saf":"13.4","samsung":"12.0","android":"79"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/clamp()"},"example":"button {\\n  font-size: clamp(1rem, 2.5vw, 2rem);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/polemius/postcss-clamp"}],"vendors_implementations":3},{"id":"color-adjust","title":"`color-adjust` Property","description":"The color-adjust property is a non-standard CSS extension that can be used to force printing of background colors and images","specification":"https://www.w3.org/TR/css-color-4/#color-adjust","stage":2,"browser_support":{"firefox":"48","and_ff":"48","kaios":"2.5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color-adjust"},"example":".background {\\n  background-color:#ccc;\\n}\\n.background.color-adjust {\\n  color-adjust: economy;\\n}\\n.background.color-adjust-exact {\\n  color-adjust: exact;\\n}","vendors_implementations":1},{"id":"color-contrast","title":"`color-contrast()` Function","description":"A function for choosing the color that contrasts the most.","specification":"https://www.w3.org/TR/css-color-5/#colorcontrast","stage":2,"browser_support":{},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/color-contrast()"},"example":"p {\\n  color: color-contrast(wheat vs tan, sienna, var(--myAccent), #d2691e);\\n}","polyfills":[],"vendors_implementations":0},{"id":"color-function","title":"`color()` Function","description":"A function that allows a color to be specified in a particular, specified color space rather than the implicit sRGB color space that most of the other color functions operate in.","specification":"https://www.w3.org/TR/css-color-4/#funcdef-color","stage":2,"browser_support":{"safari":"15","ios_saf":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/color()"},"example":"p {\\n  color: color(display-p3 1 0.5 0);\\n  color: color(display-p3 1 0.5 0 / .5);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-color-function"}],"vendors_implementations":1},{"id":"color-functional-notation","title":"Color Functional Notation","description":"A space and slash separated notation for specifying colors","specification":"https://www.w3.org/TR/css-color-4/#funcdef-rgb","stage":2,"browser_support":{"chrome":"65","and_chr":"65","edge":"79","firefox":"52","and_ff":"52","opera":"52","op_mob":"47","safari":"12.1","ios_saf":"12.2","samsung":"9.0","android":"65"},"example":"em {\\n  background-color: hsl(120deg 100% 25%);\\n  color: rgb(0 255 0);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-color-functional-notation"}],"vendors_implementations":3},{"id":"color-mix","title":"`color-mix()` Function","description":"A function for mixing colors","specification":"https://www.w3.org/TR/css-color-5/#color-mix","stage":2,"browser_support":{},"example":"p {\\n  color: color-mix(in lch, purple 50%, plum 50%);\\n}","polyfills":[],"vendors_implementations":0},{"id":"color-mod-function","title":"`color-mod()` Function","description":"A function for modifying colors","specification":"https://www.w3.org/TR/css-color-4/#funcdef-color-mod","stage":-1,"browser_support":{},"example":"p {\\n  color: color-mod(black alpha(50%));\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-color-mod-function"}],"vendors_implementations":0},{"id":"container-queries","title":"Container Queries","description":"New container property and container at rule to make changes depending on the container\'s size","specification":"https://www.w3.org/TR/css-contain-3/#container-queries","stage":0,"browser_support":{},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Container_Queries"},"example":".container {\\n  contain: layout inline-size;\\n}\\n\\n@container (min-width: 700px) {\\n  .container {\\n    /* styles applied when a container is at least 700px */\\n  }\\n}","polyfills":[{"type":"JavaScript Library","link":"https://www.npmjs.com/package/container-query-polyfill"}],"vendors_implementations":0},{"id":"custom-media-queries","title":"Custom Media Queries","description":"An at-rule for defining aliases that represent media queries","specification":"https://www.w3.org/TR/mediaqueries-5/#at-ruledef-custom-media","stage":2,"browser_support":{},"example":"@custom-media --narrow-window (max-width: 30em);\\n\\n@media (--narrow-window) {}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/postcss-custom-media"}],"vendors_implementations":0},{"id":"custom-properties","title":"Custom Properties","description":"A syntax for defining custom values accepted by all CSS properties","specification":"https://www.w3.org/TR/css-variables-1/","stage":3,"browser_support":{"edge":"16","firefox":"31","chrome":"49","safari":"10","opera":"36","ios_saf":"10","android":"49","op_mob":"64","and_chr":"49","and_ff":"31","and_uc":"12.12","samsung":"5","kaios":"2.5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/var"},"example":":root {\\n  --some-length: 32px;\\n\\n  height: var(--some-length);\\n  width: var(--some-length);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-custom-properties"}],"vendors_implementations":3},{"id":"custom-property-sets","title":"Custom Property Sets","description":"A syntax for storing properties in named variables, referenceable in other style rules","specification":"https://tabatkins.github.io/specs/css-apply-rule/","stage":-1,"browser_support":{},"example":"img {\\n  --some-length-styles: {\\n    height: 32px;\\n    width: 32px;\\n  };\\n\\n  @apply --some-length-styles;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/pascalduez/postcss-apply"}],"vendors_implementations":0},{"id":"custom-selectors","title":"Custom Selectors","description":"An at-rule for defining aliases that represent selectors","specification":"https://drafts.csswg.org/css-extensions/#custom-selectors","stage":1,"browser_support":{},"example":"@custom-selector :--heading h1, h2, h3, h4, h5, h6;\\n\\narticle :--heading + p {}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/postcss-custom-selectors"}],"vendors_implementations":0},{"id":"dir-pseudo-class","title":"`:dir` Directionality Pseudo-Class","description":"A pseudo-class for matching elements based on their directionality","specification":"https://www.w3.org/TR/selectors-4/#dir-pseudo","stage":2,"browser_support":{"firefox":"49","and_ff":"49"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:dir"},"example":"blockquote:dir(rtl) {\\n  margin-right: 10px;\\n}\\n\\nblockquote:dir(ltr) {\\n  margin-left: 10px;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-dir-pseudo-class"}],"vendors_implementations":1},{"id":"display-two-values","title":"Two values syntax for `display`","description":"Syntax that allows definition of outer and inner displays types for an element","specification":"https://www.w3.org/TR/css-display-3/#the-display-properties","stage":2,"browser_support":{"firefox":"70","safari":"15","ios_saf":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/display/two-value_syntax_of_display"},"example":".element {\\n  display: inline flow-root;\\n  display: inline flex;\\n  display: block grid;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-normalize-display-values"}],"vendors_implementations":2},{"id":"double-position-gradients","title":"Double Position Gradients","description":"A syntax for using two positions in a gradient.","specification":"https://www.w3.org/TR/css-images-4/#color-stop-syntax","stage":2,"browser_support":{"chrome":"72","and_chr":"72","edge":"79","firefox":"83","and_ff":"83","opera":"60","op_mob":"51","safari":"12.1","ios_saf":"12.2","samsung":"11.0","android":"72"},"example":".pie_chart {\\n  background-image: conic-gradient(yellowgreen 40%, gold 0deg 75%, #f06 0deg);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-double-position-gradients"}],"vendors_implementations":3},{"id":"environment-variables","title":"Custom Environment Variables","description":"A syntax for using custom values accepted by CSS globally","specification":"https://drafts.csswg.org/css-env-1/","stage":0,"browser_support":{"edge":"79","firefox":"65","chrome":"69","safari":"11.1","opera":"56","ios_saf":"11.3","android":"69","op_mob":"64","and_chr":"69","and_ff":"65","samsung":"10.1"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/env"},"example":"@media (max-width: env(--brand-small)) {\\n  body {\\n    padding: env(--brand-spacing);\\n  }\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-env-function"}],"vendors_implementations":3},{"id":"fangsong-font-family","title":"`fangsong` Font Family","description":"A generic font used for Fang Song (仿宋) typefaces in Chinese","specification":"https://www.w3.org/TR/css-fonts-4/#fangsong-def","stage":2,"browser_support":{},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/font-family#Syntax"},"example":"body {\\n  font-family: fangsong;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/JLHwung/postcss-font-family-fangsong"}],"vendors_implementations":0},{"id":"focus-visible-pseudo-class","title":"`:focus-visible` Focus-Indicated Pseudo-Class","description":"A pseudo-class for matching focused elements that indicate that focus to a user","specification":"https://www.w3.org/TR/selectors-4/#focus-visible-pseudo","stage":2,"browser_support":{"chrome":"86","and_chr":"86","edge":"86","firefox":"85","and_ff":"85","opera":"72","op_mob":"61","samsung":"14.0","android":"86"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible"},"example":":focus:not(:focus-visible) {\\n  outline: 0;\\n}","polyfills":[{"type":"JavaScript Library","link":"https://github.com/WICG/focus-visible"},{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-focus-visible"}],"vendors_implementations":2},{"id":"focus-within-pseudo-class","title":"`:focus-within` Focus Container Pseudo-Class","description":"A pseudo-class for matching elements that are either focused or that have focused descendants","specification":"https://www.w3.org/TR/selectors-4/#focus-within-pseudo","stage":2,"browser_support":{"edge":"79","firefox":"52","chrome":"60","safari":"10.1","opera":"47","ios_saf":"10.3","android":"60","op_mob":"64","and_chr":"60","and_ff":"52","samsung":"8.2","and_qq":"10.4"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-within"},"example":"form:focus-within {\\n  background: rgba(0, 0, 0, 0.3);\\n}","polyfills":[{"type":"JavaScript Library","link":"https://github.com/jsxtools/focus-within"},{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-focus-within"}],"vendors_implementations":3},{"id":"font-format-keywords","title":"Font `format()` Keywords","description":"A syntax for specifying font format as a keyword in `@font-face` rule’s `format()` function","specification":"https://www.w3.org/TR/css-fonts-4/#font-format-values","stage":1,"browser_support":{"safari":"4","ios_saf":"5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face"},"example":"@font-face {\\n  src: url(file.woff2) format(woff2);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/valtlai/postcss-font-format-keywords"}],"vendors_implementations":1},{"id":"font-variant-property","title":"`font-variant` Property","description":"A property for defining the usage of alternate glyphs in a font","specification":"https://www.w3.org/TR/css-fonts-3/#propdef-font-variant","stage":4,"browser_support":{"firefox":"34","safari":"9.1","ios_saf":"9.3","and_ff":"34","kaios":"2.5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant"},"example":"h2 {\\n  font-variant: small-caps;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/postcss-font-variant"}],"vendors_implementations":2},{"id":"gap-properties","title":"Gap Properties","description":"Properties for defining gutters within a layout","specification":"https://www.w3.org/TR/css-grid-1/#gutters","stage":3,"browser_support":{"chrome":"66","and_chr":"66","edge":"16","firefox":"61","and_ff":"61","opera":"53","op_mob":"47","safari":"12","ios_saf":"12","samsung":"9.0","android":"66"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/gap"},"example":".grid-1 {\\n  gap: 20px;\\n}\\n\\n.grid-2 {\\n  column-gap: 40px;\\n  row-gap: 20px;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-gap-properties"}],"vendors_implementations":3},{"id":"gray-function","title":"`gray()` Function","description":"A function for specifying fully desaturated colors","specification":"https://www.w3.org/TR/css-color-4/#funcdef-gray","stage":-1,"browser_support":{},"example":"p {\\n  color: gray(50);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/postcss-color-gray"}],"vendors_implementations":0},{"id":"grid-layout","title":"Grid Layout","description":"A syntax for using a grid concept to lay out content","specification":"https://www.w3.org/TR/css-grid-1/","stage":3,"browser_support":{"edge":"16","firefox":"54","chrome":"58","safari":"10.1","opera":"44","ios_saf":"10.3","android":"58","op_mob":"64","and_chr":"58","and_ff":"54","and_uc":"12.12","samsung":"6.2","and_qq":"10.4","kaios":"2.5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/grid"},"example":"section {\\n  display: grid;\\n  grid-template-columns: 100px 100px 100px;\\n  grid-gap: 10px;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/autoprefixer"}],"vendors_implementations":3},{"id":"has-pseudo-class","title":"`:has()` Relational Pseudo-Class","description":"A pseudo-class for matching ancestor and sibling elements","specification":"https://www.w3.org/TR/selectors-4/#has-pseudo","stage":2,"browser_support":{"safari":"15.4","ios_saf":"15.4"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:has"},"example":"a:has(> img) {\\n  display: block;\\n}","polyfills":[{"type":"JavaScript Library","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/css-has-pseudo"},{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/css-has-pseudo"},{"type":"Experimental Library","link":"https://github.com/csstools/postcss-plugins/tree/main/experimental/css-has-pseudo"}],"vendors_implementations":1},{"id":"hexadecimal-alpha-notation","title":"Hexadecimal Alpha Notation","description":"A 4 & 8 character hex color notation for specifying the opacity level","specification":"https://www.w3.org/TR/css-color-4/#hex-notation","stage":2,"browser_support":{"edge":"79","firefox":"49","chrome":"62","safari":"10","opera":"52","ios_saf":"10","android":"62","op_mob":"64","and_chr":"62","and_ff":"49","samsung":"8.2"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color_value#Syntax_2"},"example":"section {\\n  background-color: #f3f3f3f3;\\n  color: #0003;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-color-hex-alpha"}],"vendors_implementations":3},{"id":"hwb-function","title":"`hwb()` Function","description":"A function for specifying colors by hue and then a degree of whiteness and blackness to mix into it","specification":"https://www.w3.org/TR/css-color-4/#funcdef-hwb","stage":2,"browser_support":{"firefox":"96","and_ff":"96","safari":"15","ios_saf":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/hwb()"},"example":"p {\\n  color: hwb(120 44% 50%);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-hwb-function"}],"vendors_implementations":2},{"id":"ic-unit","title":"`ic` length unit","description":"Equal to the used advance measure of the \\"水\\" (CJK water ideograph, U+6C34) glyph found in the font used to render it","specification":"https://www.w3.org/TR/css-values-4/#ic","stage":2,"browser_support":{"firefox":"97","and_ff":"97","safari":"15.4","ios_saf":"15.4"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Values_and_Units#dimensions"},"example":"p {\\n  text-indent: 2ic;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-ic-unit"}],"vendors_implementations":2},{"id":"image-set-function","title":"`image-set()` Function","description":"A function for specifying image sources based on the user’s resolution","specification":"https://www.w3.org/TR/css-images-4/#image-set-notation","stage":2,"browser_support":{},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/image-set"},"example":"p {\\n  background-image: image-set(\\n    \\"foo.png\\" 1x,\\n    \\"foo-2x.png\\" 2x,\\n    \\"foo-print.png\\" 600dpi\\n  );\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-image-set-function"}],"vendors_implementations":0},{"id":"in-out-of-range-pseudo-class","title":"`:in-range` and `:out-of-range` Pseudo-Classes","description":"A pseudo-class for matching elements that have range limitations","specification":"https://www.w3.org/TR/selectors-4/#range-pseudos","stage":2,"browser_support":{"edge":"79","firefox":"50","chrome":"53","safari":"10.1","opera":"40","ios_saf":"10.3","android":"53","op_mob":"64","and_chr":"53","and_ff":"50","and_uc":"12.12","samsung":"5","and_qq":"10.4","baidu":"7.12"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:in-range"},"example":"input:in-range {\\n  background-color: rgba(0, 255, 0, 0.25);\\n}\\ninput:out-of-range {\\n  background-color: rgba(255, 0, 0, 0.25);\\n  border: 2px solid red;\\n}","vendors_implementations":3},{"id":"is-pseudo-class","title":"`:is()` Matches-Any Pseudo-Class","description":"A pseudo-class for matching elements in a selector list","specification":"https://www.w3.org/TR/selectors-4/#matches-pseudo","stage":2,"browser_support":{"edge":"88","firefox":"78","chrome":"88","safari":"14","opera":"75","ios_saf":"14","android":"88","op_mob":"64","and_chr":"88","and_ff":"78","samsung":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:is"},"example":"p:is(:first-child, .special) {\\n  margin-top: 1em;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-is-pseudo-class"}],"vendors_implementations":3},{"id":"lab-function","title":"`lab()` Function","description":"A function for specifying colors expressed in the CIE Lab color space","specification":"https://www.w3.org/TR/css-color-4/#funcdef-lab","stage":2,"browser_support":{"safari":"15","ios_saf":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/lab()"},"example":"body {\\n  color: lab(80% 50 20);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-lab-function"}],"vendors_implementations":1},{"id":"lch-function","title":"`lch()` Function","description":"A function for specifying colors expressed in the CIE Lab color space with chroma and hue","specification":"https://www.w3.org/TR/css-color-4/#funcdef-lch","stage":2,"browser_support":{"safari":"15","ios_saf":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/lch()"},"example":"body {\\n  color: lch(53% 105 40);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-lab-function"}],"vendors_implementations":1},{"id":"logical-properties-and-values","title":"Logical Properties and Values","description":"Flow-relative (left-to-right or right-to-left) properties and values","specification":"https://www.w3.org/TR/css-logical-1/","stage":2,"browser_support":{"edge":"89","firefox":"66","chrome":"89","safari":"15","opera":"76","ios_saf":"15","android":"89","op_mob":"64","and_chr":"89","and_ff":"66","samsung":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties"},"example":"span:first-child {\\n  float: inline-start;\\n  margin-inline-start: 10px;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-logical"}],"vendors_implementations":3},{"id":"matches-pseudo-class","title":"`:matches()` Matches-Any Pseudo-Class","description":"A pseudo-class for matching elements in a selector list","specification":"https://www.w3.org/TR/selectors-4/#selectordef-matches","stage":-1,"browser_support":{"edge":"88","firefox":"78","chrome":"88","safari":"14","opera":"75","ios_saf":"14","android":"88","op_mob":"64","and_chr":"88","and_ff":"78","samsung":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:is"},"example":"p:matches(:first-child, .special) {\\n  margin-top: 1em;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/postcss-selector-matches"}],"vendors_implementations":3},{"id":"media-query-ranges","title":"Media Query Ranges","description":"A syntax for defining media query ranges using ordinary comparison operators","specification":"https://www.w3.org/TR/mediaqueries-4/#range-context","stage":3,"browser_support":{},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/Media_Queries/Using_media_queries#Syntax_improvements_in_Level_4"},"example":"@media (width < 480px) {}\\n\\n@media (480px <= width < 768px) {}\\n\\n@media (width >= 768px) {}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/postcss-media-minmax"}],"vendors_implementations":0},{"id":"nesting-rules","title":"Nesting Rules","description":"A syntax for nesting relative rules within rules","specification":"https://www.w3.org/TR/css-nesting-1/","stage":1,"browser_support":{},"example":"article {\\n  & p {\\n    color: #333;\\n  }\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-nesting"}],"vendors_implementations":0},{"id":"not-pseudo-class","title":"`:not()` Negation List Pseudo-Class","description":"A pseudo-class for ignoring elements in a selector list","specification":"https://www.w3.org/TR/selectors-4/#negation-pseudo","stage":2,"browser_support":{"edge":"88","firefox":"84","chrome":"88","safari":"9","opera":"75","ios_saf":"9","android":"88","op_mob":"64","and_chr":"88","and_ff":"84","samsung":"15"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:not"},"example":"p:not(:first-child, .special) {\\n  margin-top: 1em;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/postcss/postcss-selector-not"}],"vendors_implementations":3},{"id":"oklab-function","title":"`oklab` and `oklch` color functions","description":"Functions that allow colors to be expressed in OKLab and OKLCH.","specification":"https://www.w3.org/TR/css-color-4/#specifying-oklab-oklch","stage":2,"browser_support":{},"example":"p {\\n  color: oklab(72.322% -0.0465 -0.1150);\\n  color: oklch(72.322% 0.12403 247.996);\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-oklab-function"}],"vendors_implementations":0},{"id":"opacity-percentage","title":"Support for percentages for `opacity`","description":"Syntactic sugar to use percentages instead of a float between 0 and 1.","specification":"https://www.w3.org/TR/css-color-4/#transparency","stage":2,"browser_support":{"chrome":"78","and_chr":"78","edge":"79","firefox":"70","samsung":"12.0","android":"78"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/opacity"},"example":"img {\\n  opacity: 90%;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/mrcgrtz/postcss-opacity-percentage"}],"vendors_implementations":2},{"id":"overflow-property","title":"`overflow` Shorthand Property","description":"A property for defining `overflow-x` and `overflow-y`","specification":"https://www.w3.org/TR/css-overflow-3/#propdef-overflow","stage":2,"browser_support":{"chrome":"68","and_chr":"68","edge":"79","firefox":"61","and_ff":"61","opera":"55","op_mob":"48","samsung":"10.0","android":"68"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/overflow"},"example":"html {\\n  overflow: hidden auto;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-overflow-shorthand"}],"vendors_implementations":2},{"id":"overflow-wrap-property","title":"`overflow-wrap` Property","description":"A property for defining whether to insert line breaks within words to prevent overflowing","specification":"https://www.w3.org/TR/css-text-3/#overflow-wrap-property","stage":2,"browser_support":{"edge":"18","firefox":"49","chrome":"23","safari":"6.1","opera":"12.1","ios_saf":"7","android":"4.4","bb":"10","op_mob":"64","and_chr":"23","and_ff":"49","and_uc":"12.12","samsung":"4","and_qq":"10.4","baidu":"7.12"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/overflow-wrap"},"example":"p {\\n  overflow-wrap: break-word;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/mattdimu/postcss-replace-overflow-wrap"}],"vendors_implementations":3},{"id":"overscroll-behavior-property","title":"`overscroll-behavior` Property","description":"Properties for controlling when the scroll position of a scroll container reaches the edge of a scrollport","specification":"https://www.w3.org/TR/css-overscroll-1/","stage":1,"browser_support":{"edge":"79","firefox":"59","chrome":"65","opera":"52","android":"65","op_mob":"64","and_chr":"65","and_ff":"59","samsung":"8.2","and_qq":"10.4"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/overscroll-behavior"},"example":".messages {\\n  height: 220px;\\n  overflow: auto;\\n  overscroll-behavior-y: contain;\\n}\\n\\nbody {\\n  margin: 0;\\n  overscroll-behavior: none;\\n}","vendors_implementations":2},{"id":"place-properties","title":"Place Properties","description":"Properties for defining alignment within a layout","specification":"https://www.w3.org/TR/css-align-3/#place-items-property","stage":2,"browser_support":{"chrome":"59","and_chr":"59","edge":"79","firefox":"53","and_ff":"53","opera":"46","op_mob":"43","safari":"11","ios_saf":"11","samsung":"7.0","android":"59"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/place-content"},"example":".example {\\n  place-content: flex-end;\\n  place-items: center / space-between;\\n  place-self: flex-start / center;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-place"}],"vendors_implementations":3},{"id":"prefers-color-scheme-query","title":"`prefers-color-scheme` Media Query","description":"A media query to detect if the user has requested the system use a light or dark color theme","specification":"https://www.w3.org/TR/mediaqueries-5/#prefers-color-scheme","stage":2,"browser_support":{"edge":"79","firefox":"67","chrome":"76","safari":"12.1","opera":"62","ios_saf":"13","android":"76","op_mob":"64","and_chr":"76","and_ff":"67","samsung":"12"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-color-scheme"},"example":"body {\\n  background-color: white;\\n  color: black;\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  body {\\n    background-color: black;\\n    color: white;\\n  }\\n}","polyfills":[{"type":"JavaScript Library","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/css-prefers-color-scheme"},{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/css-prefers-color-scheme"}],"vendors_implementations":3},{"id":"prefers-reduced-motion-query","title":"`prefers-reduced-motion` Media Query","description":"A media query to detect if the user has requested less animation and general motion on the page","specification":"https://www.w3.org/TR/mediaqueries-5/#prefers-reduced-motion","stage":2,"browser_support":{"edge":"79","firefox":"63","chrome":"74","safari":"10.1","opera":"64","ios_saf":"10.3","android":"74","op_mob":"64","and_chr":"74","and_ff":"63","samsung":"11.1"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion"},"example":".animation {\\n  animation: vibrate 0.3s linear infinite both; \\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .animation {\\n    animation: none;\\n  }\\n}","vendors_implementations":3},{"id":"read-only-write-pseudo-class","title":"`:read-only` and `:read-write` selectors","description":"Pseudo-classes to match elements which are considered user-alterable","specification":"https://www.w3.org/TR/selectors-4/#rw-pseudos","stage":2,"browser_support":{"edge":"13","firefox":"78","chrome":"36","safari":"9","opera":"23","ios_saf":"9","android":"36","bb":"10","op_mob":"64","and_chr":"36","and_ff":"78","and_uc":"12.12","samsung":"4","and_qq":"10.4","baidu":"7.12"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:read-only"},"example":"input:read-only {\\n  background-color: #ccc;\\n}","vendors_implementations":3},{"id":"rebeccapurple-color","title":"`rebeccapurple` Color","description":"A particularly lovely shade of purple in memory of Rebecca Alison Meyer","specification":"https://www.w3.org/TR/css-color-4/#valdef-color-rebeccapurple","stage":2,"browser_support":{"edge":"12","firefox":"33","chrome":"38","safari":"7","opera":"25","ios_saf":"8","android":"4.4","op_mob":"64","and_chr":"38","and_ff":"33","and_uc":"12.12","samsung":"4","and_qq":"10.4","baidu":"7.12","kaios":"2.5"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/color_value"},"example":"html {\\n  color: rebeccapurple;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-color-rebeccapurple"}],"vendors_implementations":3},{"id":"system-ui-font-family","title":"`system-ui` Font Family","description":"A generic font used to match the user’s interface","specification":"https://www.w3.org/TR/css-fonts-4/#system-ui-def","stage":2,"browser_support":{"edge":"79","firefox":"92","chrome":"56","safari":"11","opera":"43","ios_saf":"11","android":"56","op_mob":"64","and_chr":"56","and_ff":"92","and_uc":"12.12","samsung":"6.2","and_qq":"10.4"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/font-family#Syntax"},"example":"body {\\n  font-family: system-ui;\\n}","polyfills":[{"type":"PostCSS Plugin","link":"https://github.com/JLHwung/postcss-font-family-system-ui"}],"vendors_implementations":3},{"id":"unset-value","title":"`unset` Keyword","description":"The unset CSS keyword resets a property to its inherited value if the property naturally inherits from its parent, and to its initial value if not.","specification":"https://www.w3.org/TR/css-cascade-4/#inherit-initial","stage":3,"browser_support":{"chrome":"41","and_chr":"41","edge":"13","firefox":"27","and_ff":"27","opera":"28","op_mob":"28","safari":"9.1","ios_saf":"9.3","samsung":"4.0","android":"41"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/unset"},"example":"div {\\n  border-color: unset;\\n  color: unset;\\n}","vendors_implementations":3},{"id":"when-else-rules","title":"When/Else Rules","description":"At-rules for specifying media queries and support queries in a single grammar","specification":"https://www.w3.org/TR/2021/WD-css-conditional-5-20211221/","stage":2,"browser_support":{},"example":"@when media(width >= 640px) and (supports(display: flex) or supports(display: grid)) {\\n  /* A */\\n} @else media(pointer: coarse) {\\n  /* B */\\n} @else {\\n  /* C */\\n}","vendors_implementations":0},{"id":"where-pseudo-class","title":"`:where()` Zero-Specificity Pseudo-Class","description":"A pseudo-class for matching elements in a selector list without contributing specificity","specification":"https://www.w3.org/TR/selectors-4/#where-pseudo","stage":2,"browser_support":{"chrome":"88","and_chr":"88","edge":"88","firefox":"82","and_ff":"82","opera":"74","op_mob":"63","safari":"14","ios_saf":"14","samsung":"15.0","android":"88"},"docs":{"mdn":"https://developer.mozilla.org/en-US/docs/Web/CSS/:where"},"example":"a:where(:not(:hover)) {\\n  text-decoration: none;\\n}","vendors_implementations":3}]')},9717:_=>{"use strict";_.exports=JSON.parse('[{"prop":"animation","initial":"${animation-name} ${animation-duration} ${animation-timing-function} ${animation-delay} ${animation-iteration-count} ${animation-direction} ${animation-fill-mode} ${animation-play-state}","combined":true},{"prop":"animation-delay","initial":"0s"},{"prop":"animation-direction","initial":"normal"},{"prop":"animation-duration","initial":"0s"},{"prop":"animation-fill-mode","initial":"none"},{"prop":"animation-iteration-count","initial":"1"},{"prop":"animation-name","initial":"none"},{"prop":"animation-play-state","initial":"running"},{"prop":"animation-timing-function","initial":"ease"},{"prop":"backface-visibility","initial":"visible","basic":true},{"prop":"background","initial":"${background-color} ${background-image} ${background-repeat} ${background-position} / ${background-size} ${background-origin} ${background-clip} ${background-attachment}","combined":true},{"prop":"background-attachment","initial":"scroll"},{"prop":"background-clip","initial":"border-box"},{"prop":"background-color","initial":"transparent"},{"prop":"background-image","initial":"none"},{"prop":"background-origin","initial":"padding-box"},{"prop":"background-position","initial":"0 0"},{"prop":"background-position-x","initial":"0"},{"prop":"background-position-y","initial":"0"},{"prop":"background-repeat","initial":"repeat"},{"prop":"background-size","initial":"auto auto"},{"prop":"border","initial":"${border-width} ${border-style} ${border-color}","combined":true},{"prop":"border-style","initial":"none"},{"prop":"border-width","initial":"medium"},{"prop":"border-color","initial":"currentColor"},{"prop":"border-bottom","initial":"0"},{"prop":"border-bottom-color","initial":"currentColor"},{"prop":"border-bottom-left-radius","initial":"0"},{"prop":"border-bottom-right-radius","initial":"0"},{"prop":"border-bottom-style","initial":"none"},{"prop":"border-bottom-width","initial":"medium"},{"prop":"border-collapse","initial":"separate","basic":true,"inherited":true},{"prop":"border-image","initial":"none","basic":true},{"prop":"border-left","initial":"0"},{"prop":"border-left-color","initial":"currentColor"},{"prop":"border-left-style","initial":"none"},{"prop":"border-left-width","initial":"medium"},{"prop":"border-radius","initial":"0","basic":true},{"prop":"border-right","initial":"0"},{"prop":"border-right-color","initial":"currentColor"},{"prop":"border-right-style","initial":"none"},{"prop":"border-right-width","initial":"medium"},{"prop":"border-spacing","initial":"0","basic":true,"inherited":true},{"prop":"border-top","initial":"0"},{"prop":"border-top-color","initial":"currentColor"},{"prop":"border-top-left-radius","initial":"0"},{"prop":"border-top-right-radius","initial":"0"},{"prop":"border-top-style","initial":"none"},{"prop":"border-top-width","initial":"medium"},{"prop":"bottom","initial":"auto","basic":true},{"prop":"box-shadow","initial":"none","basic":true},{"prop":"box-sizing","initial":"content-box","basic":true},{"prop":"caption-side","initial":"top","basic":true,"inherited":true},{"prop":"clear","initial":"none","basic":true},{"prop":"clip","initial":"auto","basic":true},{"prop":"color","initial":"#000","basic":true},{"prop":"columns","initial":"auto","basic":true},{"prop":"column-count","initial":"auto","basic":true},{"prop":"column-fill","initial":"balance","basic":true},{"prop":"column-gap","initial":"normal","basic":true},{"prop":"column-rule","initial":"${column-rule-width} ${column-rule-style} ${column-rule-color}","combined":true},{"prop":"column-rule-color","initial":"currentColor"},{"prop":"column-rule-style","initial":"none"},{"prop":"column-rule-width","initial":"medium"},{"prop":"column-span","initial":"1","basic":true},{"prop":"column-width","initial":"auto","basic":true},{"prop":"content","initial":"normal","basic":true},{"prop":"counter-increment","initial":"none","basic":true},{"prop":"counter-reset","initial":"none","basic":true},{"prop":"cursor","initial":"auto","basic":true,"inherited":true},{"prop":"direction","initial":"ltr","basic":true,"inherited":true},{"prop":"display","initial":"inline","basic":true},{"prop":"empty-cells","initial":"show","basic":true,"inherited":true},{"prop":"float","initial":"none","basic":true},{"prop":"font","contains":["font-style","font-variant","font-weight","font-stretch","font-size","line-height","font-family"],"basic":true,"inherited":true},{"prop":"font-family","initial":"serif"},{"prop":"font-size","initial":"medium"},{"prop":"font-style","initial":"normal"},{"prop":"font-variant","initial":"normal"},{"prop":"font-weight","initial":"normal"},{"prop":"font-stretch","initial":"normal"},{"prop":"line-height","initial":"normal","inherited":true},{"prop":"height","initial":"auto","basic":true},{"prop":"hyphens","initial":"none","basic":true,"inherited":true},{"prop":"left","initial":"auto","basic":true},{"prop":"letter-spacing","initial":"normal","basic":true,"inherited":true},{"prop":"list-style","initial":"${list-style-type} ${list-style-position} ${list-style-image}","combined":true,"inherited":true},{"prop":"list-style-image","initial":"none"},{"prop":"list-style-position","initial":"outside"},{"prop":"list-style-type","initial":"disc"},{"prop":"margin","initial":"0","basic":true},{"prop":"margin-bottom","initial":"0"},{"prop":"margin-left","initial":"0"},{"prop":"margin-right","initial":"0"},{"prop":"margin-top","initial":"0"},{"prop":"max-height","initial":"none","basic":true},{"prop":"max-width","initial":"none","basic":true},{"prop":"min-height","initial":"0","basic":true},{"prop":"min-width","initial":"0","basic":true},{"prop":"opacity","initial":"1","basic":true},{"prop":"orphans","initial":"2","basic":true},{"prop":"outline","initial":"${outline-width} ${outline-style} ${outline-color}","combined":true},{"prop":"outline-color","initial":"invert"},{"prop":"outline-style","initial":"none"},{"prop":"outline-width","initial":"medium"},{"prop":"overflow","initial":"visible","basic":true},{"prop":"overflow-x","initial":"visible","basic":true},{"prop":"overflow-y","initial":"visible","basic":true},{"prop":"padding","initial":"0","basic":true},{"prop":"padding-bottom","initial":"0"},{"prop":"padding-left","initial":"0"},{"prop":"padding-right","initial":"0"},{"prop":"padding-top","initial":"0"},{"prop":"page-break-after","initial":"auto","basic":true},{"prop":"page-break-before","initial":"auto","basic":true},{"prop":"page-break-inside","initial":"auto","basic":true},{"prop":"perspective","initial":"none","basic":true},{"prop":"perspective-origin","initial":"50% 50%","basic":true},{"prop":"position","initial":"static","basic":true},{"prop":"quotes","initial":"“ ” ‘ ’"},{"prop":"right","initial":"auto","basic":true},{"prop":"tab-size","initial":"8","basic":true,"inherited":true},{"prop":"table-layout","initial":"auto","basic":true},{"prop":"text-align","initial":"left","basic":true,"inherited":true},{"prop":"text-align-last","initial":"auto","basic":true,"inherited":true},{"prop":"text-decoration","initial":"${text-decoration-line}","combined":true},{"prop":"text-decoration-color","initial":"inherited"},{"prop":"text-decoration-color","initial":"currentColor"},{"prop":"text-decoration-line","initial":"none"},{"prop":"text-decoration-style","initial":"solid"},{"prop":"text-indent","initial":"0","basic":true,"inherited":true},{"prop":"text-shadow","initial":"none","basic":true,"inherited":true},{"prop":"text-transform","initial":"none","basic":true,"inherited":true},{"prop":"top","initial":"auto","basic":true},{"prop":"transform","initial":"none","basic":true},{"prop":"transform-origin","initial":"50% 50% 0","basic":true},{"prop":"transform-style","initial":"flat","basic":true},{"prop":"transition","initial":"${transition-property} ${transition-duration} ${transition-timing-function} ${transition-delay}","combined":true},{"prop":"transition-delay","initial":"0s"},{"prop":"transition-duration","initial":"0s"},{"prop":"transition-property","initial":"none"},{"prop":"transition-timing-function","initial":"ease"},{"prop":"unicode-bidi","initial":"normal","basic":true},{"prop":"vertical-align","initial":"baseline","basic":true},{"prop":"visibility","initial":"visible","basic":true,"inherited":true},{"prop":"white-space","initial":"normal","basic":true,"inherited":true},{"prop":"widows","initial":"2","basic":true,"inherited":true},{"prop":"width","initial":"auto","basic":true},{"prop":"word-spacing","initial":"normal","basic":true,"inherited":true},{"prop":"z-index","initial":"auto","basic":true}]')}};var X={};function __nccwpck_require__(ee){var te=X[ee];if(te!==undefined){return te.exports}var re=X[ee]={exports:{}};var se=true;try{_[ee].call(re.exports,re,re.exports,__nccwpck_require__);se=false}finally{if(se)delete X[ee]}return re.exports}(()=>{__nccwpck_require__.o=(_,X)=>Object.prototype.hasOwnProperty.call(_,X)})();if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var ee=__nccwpck_require__(1097);module.exports=ee})();